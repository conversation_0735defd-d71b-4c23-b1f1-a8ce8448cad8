/* eslint-disable @typescript-eslint/naming-convention */
export const entityLogos: Record<string, string> = {
  Bitfy:
    "https://d19rvpz5ft41ub.cloudfront.net/public/imgs/Entities/3/Logo.png",
  Gigadat:
    "https://d19rvpz5ft41ub.cloudfront.net/public/imgs/Entities/1/Logo.png",
  Wyzia:
    "https://d19rvpz5ft41ub.cloudfront.net/public/imgs/Entities/2/Logo.png",
};

export const entityBackgroundStyles: Record<string, string> = {
  Bitfy: "linear-gradient(90deg, rgba(2,46,87,1) 0%, rgba(110,4,181,1) 100%)",
  Gigadat: "linear-gradient(90deg, rgba(10,4,0,1) 0%, rgba(48,20,1,1) 100%)",
  Wyzia: "linear-gradient(90deg, rgba(3,1,0,1) 0%, rgba(232,133,28,1) 100%)",
};
