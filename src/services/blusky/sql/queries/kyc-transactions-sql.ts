import { formatDateWithCommas } from "@utils/date-only";

const getKycTransactionsSql = (
  serviceNumber: string,
  fromDate: Date,
  toDate: Date
) => {
  return `
      SELECT
       DISTINCT
       kyc.SERVICE_NUMBER AS serviceNumber,
       vkt.Name,
       vkt.Email,
       vkt.Platform AS Type,
       vkt.clientUserId,
       vkt.status,
       vkt.DispCreated,
       vkt.DispUpdated
    FROM [VW_TRANSACTION_CAMPAIGNTBL] AS kyc
    JOIN [VW_VERIFYKYCTRACKING] AS vkt
    ON kyc.SERVICE_NUMBER = vkt.[serviceNumber]
   WHERE
     kyc.SERVICE_NUMBER IN ('${serviceNumber}')
     AND vkt.[Date]  BETWEEN CREATEDATE(${formatDateWithCommas(
       fromDate
     )}) AND CREATEDATE(${formatDateWithCommas(toDate)})
     AND vkt.billableMerchant = 1
   `;
};

export { getKycTransactionsSql };
