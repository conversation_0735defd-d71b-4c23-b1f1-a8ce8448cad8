import { type Platform } from "@constants/transactions/platform";

import { getAuthToken } from "./authentication";
import { fetchJaqlData } from "./jaql/fetch-jaql-data";
import { getFirstTransactionDateJaql } from "./jaql/queries/first-transaction-date-jaql";
import { getKycTxnJaql } from "./jaql/queries/kyc-transactions-jaql";
import { getTransactionsJaql } from "./jaql/queries/transactions-jaql";
import { getTransactionTotalAmountJaql } from "./jaql/queries/transactions-total-jaql";
import { fetchSqlData } from "./sql/fetch-sql-data";
import { getFirstTransactionDateSql } from "./sql/queries/first-transaction-date-sql";
import { getKycTransactionsSql } from "./sql/queries/kyc-transactions-sql";
import { getTransactionsSql } from "./sql/queries/transactions-sql";
import { getTransactionsTotalSql } from "./sql/queries/transactions-total-sql";
import { toDateOnly, type DateOnly, dateToString } from "../../utils/date-only";

type Transaction = {
  createdDate: string;
  updatedDate: string;
  serviceNumber: string;
  originalAmt: number;
  refundAmt: number;
  finalAmt: number;
  currency: string;
  country: string;
  billable: boolean;
  platform: string;
  custNumber: string;
  rcode: number;
  integratorName: string;
  programName: string;
  billingName: string;
  transactionID: string;
  receiptID: string;
  interacRef: string;
  status: string;
};

export type KycTxn = {
  serviceNumber: string;
  name: string;
  email: string;
  type: string;
  clientUserId: string;
  status: string;
  dispCreated: string;
  dispUpdated: string;
};

const transactionStatus = {
  success: "STATUS_SUCCESS",
  failed: "STATUS_FAILED",
  refund: "STATUS_REFUND",
  fRefund: "STATUS_F_REFUNDED",
  refunded: "STATUS_REFUNDED",
  rejected1: "STATUS_REJECTED1",
} as const;

type TransactionStatus =
  (typeof transactionStatus)[keyof typeof transactionStatus];

const getTransactionsTotalAmount = async (
  serviceNumbers: string[],
  fromDate: DateOnly,
  toDate: DateOnly,
  token: string
): Promise<Record<Platform, number>> => {
  const rawSql = getTransactionsTotalSql(serviceNumbers, fromDate, toDate);
  const response = await fetchSqlData(rawSql, token);

  const result: Record<string, number> = {};

  for (const row of response) {
    const { type, totalFinalAmount } = row;

    if (type) {
      result[type] = Number(totalFinalAmount) || 0;
    }
  }

  return result;
};

const getTransactionsTotalAmountWithJaql = async (
  serviceNumbers: string[],
  fromDate: DateOnly,
  toDate: DateOnly,
  token: string
): Promise<Record<Platform, number>> => {
  const jaql = getTransactionTotalAmountJaql(serviceNumbers, fromDate, toDate);

  const response = await fetchJaqlData(jaql, token);
  const result: Record<string, number> = {};

  for (const row of response) {
    const { type, totalFinalAmt } = row;

    if (type) {
      result[type] = Number(totalFinalAmt) || 0;
    }
  }

  return result;
};

const getTransactions = async (
  serviceNumber: string,
  interval: { fromDate: DateOnly; toDate: DateOnly },
  options?: {
    token?: string;
    aggregate?: Partial<
      Record<
        TransactionStatus,
        {
          aggregateForPlatforms: string[];
          targetPlatform: string;
          keepInOriginal?: boolean;
        }
      >
    >;
  }
): Promise<Record<string, Transaction[]>> => {
  const rawSql = getTransactionsSql(
    serviceNumber,
    interval.fromDate,
    interval.toDate
  );

  const response = await fetchSqlData(
    rawSql,
    options?.token ?? (await getAuthToken())
  );

  const result: Partial<Record<string, Transaction[]>> = {};

  for (const transaction of response) {
    const platform = transaction["platform"] as string;
    const status = transaction["status"] as TransactionStatus | undefined;

    // Convert billable from string (case-insensitive) or boolean to boolean
    const billableBoolean =
      typeof transaction["billable"] === "boolean"
        ? transaction["billable"]
        : transaction["billable"]?.toString().toLowerCase() === "true";

    const transactionWithBooleanBillable: Transaction = {
      ...(transaction as unknown as Transaction),
      billable: billableBoolean,
    };

    if (status && options?.aggregate?.[status]) {
      const { aggregateForPlatforms, targetPlatform, keepInOriginal } = options
        .aggregate[status] as {
        aggregateForPlatforms: string[];
        targetPlatform: string;
        keepInOriginal?: boolean;
      };

      if (aggregateForPlatforms.includes(platform)) {
        // Shorthand to check if the result[platform] array exists and,
        // if it doesn't, initializing it as an empty array
        result[targetPlatform] ||= [];
        result[targetPlatform]!.push(transactionWithBooleanBillable);

        if (!keepInOriginal) {
          continue;
        }
      }
    }

    result[platform] ||= [];
    result[platform]!.push(transactionWithBooleanBillable);
  }

  return result as Record<string, Transaction[]>;
};

const getTransactionsWithJaql = async (
  serviceNumber: string,
  interval: { fromDate: DateOnly; toDate: DateOnly },
  options?: {
    token?: string;
    aggregate?: Partial<
      Record<
        TransactionStatus,
        {
          aggregateForPlatforms: string[];
          targetPlatform: string;
          keepInOriginal?: boolean;
        }
      >
    >;
  }
): Promise<Record<string, Transaction[]>> => {
  const jaql = getTransactionsJaql(
    serviceNumber,
    interval.fromDate,
    interval.toDate
  );

  const token = options?.token ?? (await getAuthToken());

  const response = await fetchJaqlData(jaql, token);

  const result: Partial<Record<string, Transaction[]>> = {};

  for (const transaction of response) {
    const platform = transaction["platform"] as string;
    const status = transaction["status"] as TransactionStatus | undefined;

    // Convert billable from string (case-insensitive) or boolean to boolean
    const billableBoolean =
      typeof transaction["billable"] === "boolean"
        ? transaction["billable"]
        : transaction["billable"]?.toString().toLowerCase() === "true";

    const transactionWithBooleanBillable: Transaction = {
      ...(transaction as unknown as Transaction),
      billable: billableBoolean,
    };

    if (status && options?.aggregate?.[status]) {
      const { aggregateForPlatforms, targetPlatform, keepInOriginal } = options
        .aggregate[status] as {
        aggregateForPlatforms: string[];
        targetPlatform: string;
        keepInOriginal?: boolean;
      };

      if (aggregateForPlatforms.includes(platform)) {
        // Shorthand to check if the result[platform] array exists and,
        // if it doesn't, initializing it as an empty array
        result[targetPlatform] ||= [];
        result[targetPlatform]!.push(transactionWithBooleanBillable);

        if (!keepInOriginal) {
          continue;
        }
      }
    }

    result[platform] ||= [];
    result[platform]!.push(transactionWithBooleanBillable);
  }

  return result as Record<string, Transaction[]>;
};

const getFirstTransactionEver = async (
  serviceNumber: string,
  token: string
): Promise<DateOnly | undefined> => {
  const sql = getFirstTransactionDateSql(serviceNumber);
  const response = await fetchSqlData(sql, token);
  let result: DateOnly | undefined;

  for (const row of response) {
    const { serviceNumber: serviceNumber_, firstTransaction } = row;

    if (serviceNumber === serviceNumber_) {
      result = toDateOnly(new Date(firstTransaction as string));
      break;
    }
  }

  return result;
};

const getFirstTransactionEverWithJaql = async (
  serviceNumber: string,
  token: string
): Promise<DateOnly | undefined> => {
  const jaql = getFirstTransactionDateJaql(serviceNumber);
  const response = await fetchJaqlData(jaql, token);
  let result: DateOnly | undefined;

  for (const row of response) {
    const { serviceNumber: serviceNumber_, firstTransaction } = row;

    if (serviceNumber === serviceNumber_) {
      result = toDateOnly(new Date(firstTransaction as string));
      break;
    }
  }

  return result;
};

const getKycTransactions = async (
  serviceNumber: string,
  fromDate: Date,
  toDate: Date,
  token: string
): Promise<KycTxn[]> => {
  const rawSql = getKycTransactionsSql(serviceNumber, fromDate, toDate);

  return (await fetchSqlData(rawSql, token)) as KycTxn[];
};

const getKycTransactionsWithJaql = async (
  serviceNumber: string,
  fromDateString: Date,
  toDateString: Date,
  token: string
): Promise<KycTxn[]> => {
  const formattedFromDate = dateToString(fromDateString);
  const formattedToDate = dateToString(toDateString);
  const jaql = getKycTxnJaql(serviceNumber, formattedFromDate, formattedToDate);

  return (await fetchJaqlData(jaql, token)) as KycTxn[];
};

export {
  getTransactionsTotalAmount,
  getTransactionsTotalAmountWithJaql,
  getTransactions,
  getFirstTransactionEver,
  getTransactionsWithJaql,
  getKycTransactions,
  getKycTransactionsWithJaql,
  getFirstTransactionEverWithJaql,
  type Transaction,
  type TransactionStatus,
  transactionStatus,
};
