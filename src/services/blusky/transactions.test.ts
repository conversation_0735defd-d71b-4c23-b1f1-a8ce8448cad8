import * as getAuthToken from "./authentication";
import * as fetchSqlData from "./sql/fetch-sql-data";
import * as getFirstTransactionDateSql from "./sql/queries/first-transaction-date-sql";
import * as getTransactionsTotalSql from "./sql/queries/transactions-total-sql";
import {
  getKycTransactions,
  getFirstTransactionEver,
  getTransactions,
  getTransactionsTotalAmount,
  transactionStatus,
} from "./transactions";

describe("getKycTransactions", () => {
  it("should return KYC transactions", async () => {
    const spyFetchSqlData = vi
      .spyOn(fetchSqlData, "fetchSqlData")
      .mockResolvedValue([
        {
          clientUserId: "1234567890",
          dispCreated: "2024-08-02 02:03:10",
          dispUpdated: "2024-08-02 02:14:06",
          email: "<EMAIL>",

          serviceNumber: "99912345678",
          status: "STATUS_SUCCESS",
          type: "KY4",
        },
        {
          clientUserId: "0987654321",
          dispCreated: "2024-08-02 02:03:10",
          dispUpdated: "2024-08-02 02:14:06",
          email: "<EMAIL>",

          serviceNumber: "99912345678",
          status: "STATUS_SUCCESS",
          type: "KY5",
        },
      ]);

    const result = await getKycTransactions(
      "99912345678",
      new Date(2024, 7, 1),
      new Date(2024, 7, 2),
      "abc"
    );

    expect(spyFetchSqlData).toHaveBeenCalledWith(
      `
      SELECT
       DISTINCT
       kyc.SERVICE_NUMBER AS serviceNumber,
       vkt.Name,
       vkt.Email,
       vkt.Platform AS Type,
       vkt.clientUserId,
       vkt.status,
       vkt.DispCreated,
       vkt.DispUpdated
    FROM [VW_TRANSACTION_CAMPAIGNTBL] AS kyc
    JOIN [VW_VERIFYKYCTRACKING] AS vkt
    ON kyc.SERVICE_NUMBER = vkt.[serviceNumber]
   WHERE
     kyc.SERVICE_NUMBER IN ('99912345678')
     AND vkt.[Date]  BETWEEN CREATEDATE(2024,08,01) AND CREATEDATE(2024,08,02)
     AND vkt.billableMerchant = 1
   `,
      "abc"
    );

    expect(result).toEqual([
      {
        clientUserId: "1234567890",
        dispCreated: "2024-08-02 02:03:10",
        dispUpdated: "2024-08-02 02:14:06",
        email: "<EMAIL>",
        serviceNumber: "99912345678",
        status: "STATUS_SUCCESS",
        type: "KY4",
      },
      {
        clientUserId: "0987654321",
        dispCreated: "2024-08-02 02:03:10",
        dispUpdated: "2024-08-02 02:14:06",
        email: "<EMAIL>",
        serviceNumber: "99912345678",
        status: "STATUS_SUCCESS",
        type: "KY5",
      },
    ]);
  });
});

describe("getTransactionsTotalAmount", () => {
  it("should return total amounts for each platform", async () => {
    const serviceNumbers = ["1234567890", "9876543210"];
    const fromDate = {
      year: 2023,
      month: 1,
      day: 1,
    };
    const toDate = {
      year: 2023,
      month: 12,
      day: 31,
    };

    const spyGetTransactionsTotalSql = vi.spyOn(
      getTransactionsTotalSql,
      "getTransactionsTotalSql"
    );
    const spyFetchSqlData = vi
      .spyOn(fetchSqlData, "fetchSqlData")
      .mockResolvedValue([
        {
          type: "PlatformA",
          totalFinalAmount: 1000,
        },
        {
          type: "PlatformB",
          totalFinalAmount: 500,
        },
      ]);

    const result = await getTransactionsTotalAmount(
      serviceNumbers,
      fromDate,
      toDate,
      "abc"
    );

    expect(result).toEqual({
      // eslint-disable-next-line @typescript-eslint/naming-convention
      PlatformA: 1000,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      PlatformB: 500,
    });

    expect(spyGetTransactionsTotalSql).toHaveBeenCalledWith(
      serviceNumbers,
      fromDate,
      toDate
    );

    expect(spyFetchSqlData).toHaveBeenCalledWith(
      `
  SELECT 
    f.platform as type,
    SUM(f.[FINAL_AMT]) AS totalFinalAmount
    FROM [transactions] f
    WHERE STATUS IN ('STATUS_SUCCESS')
    AND f.[UPDATEDAT] BETWEEN createdate(2023,01,01) AND createdate(2023,12,31)
    AND f.[SERVICE_NUMBER] IN ('1234567890', '9876543210')
    GROUP BY f.[PLATFORM]
  `,
      "abc"
    );
  });
});

describe("getTransactions", () => {
  it("should return transactions for each platform with no options", async () => {
    const spyFetchSqlData = vi
      .spyOn(fetchSqlData, "fetchSqlData")
      .mockResolvedValue([
        {
          platform: "RTO",
          totalFinalAmount: 1000,
        },
        {
          platform: "ETI",
          totalFinalAmount: 500,
        },
      ]);

    const spyGetAuthToken = vi
      .spyOn(getAuthToken, "getAuthToken")
      .mockResolvedValue("abc");

    const result = await getTransactions("1234567890", {
      fromDate: { year: 2023, month: 1, day: 1 },
      toDate: { year: 2023, month: 12, day: 31 },
    });

    expect(result).toEqual({
      // eslint-disable-next-line @typescript-eslint/naming-convention
      RTO: [
        {
          platform: "RTO",
          totalFinalAmount: 1000,
          billable: false,
        },
      ],
      // eslint-disable-next-line @typescript-eslint/naming-convention
      ETI: [
        {
          platform: "ETI",
          totalFinalAmount: 500,
          billable: false,
        },
      ],
    });

    expect(spyFetchSqlData).toHaveBeenCalledWith(
      `
      SELECT
       A.[CREATED_DATE]    AS Created_Date,
       A.[UPDATED_DATE]    AS Updated_Date,
       A.[SERVICE_NUMBER]  AS Service_Number,
       A.[ORIGINAL_AMT]    AS Original_Amt,
       A.[REFUND_AMT]      AS Refund_Amt,
       A.[FINAL_AMT]       AS Final_Amt,
       A.[CURRENCY]        AS Currency,
       A.[COUNTRY]         AS Country,
       A.[BILLABLE]        AS Billable,
       A.[PLATFORM]        AS Platform,
       A.[CUST_NUMBER]     AS Cust_Number,
       A.[RCODE]           AS Rcode,
       A.[INTEGRATOR_NAME] AS Integrator_Name,
       A.[PROGRAM_NAME]    AS Program_Name,
       A.[BILLING_NAME]    AS Billing_Name,
       A.[TRANSACTION_ID]  AS Transaction_ID,
       A.[RECEIPT_ID]      AS Receipt_ID,
       A.[INTERAC_REF]     AS Interac_Ref,
       A.[FI_NAME]         AS FI_Name,
       A.[STATUS]          AS Status
        FROM [transactions] A
        WHERE A.[SERVICE_NUMBER] IN ('1234567890')
        AND 
        (updatedat BETWEEN  
        CreateDate(2023,01,01) AND CreateDate(2023,12,31)
        OR createdat BETWEEN  
        CreateDate(2023,01,01) AND CreateDate(2023,12,31)
      )
   `,
      "abc"
    );

    expect(spyGetAuthToken).toHaveBeenCalledTimes(1);
  });

  it("should return transactions for each platform with options", async () => {
    const spyFetchSqlData = vi
      .spyOn(fetchSqlData, "fetchSqlData")
      .mockResolvedValue([
        {
          platform: "RFM",
          totalFinalAmount: 1000,
          billable: "true",
        },
        {
          platform: "ETI",
          totalFinalAmount: 500,
          status: transactionStatus.rejected1,
          billable: "false",
        },
      ]);

    const spyGetAuthToken = vi
      .spyOn(getAuthToken, "getAuthToken")
      .mockResolvedValue("abc");

    const result = await getTransactions(
      "1234567890",
      {
        fromDate: { year: 2023, month: 1, day: 1 },
        toDate: { year: 2023, month: 12, day: 31 },
      },
      {
        aggregate: {
          [transactionStatus.rejected1]: {
            aggregateForPlatforms: ["ETI", "RFM"],
            targetPlatform: "RTO",
            keepInOriginal: false,
          },
        },
      }
    );

    expect(result).toEqual({
      // eslint-disable-next-line @typescript-eslint/naming-convention
      RTO: [
        {
          platform: "ETI",
          totalFinalAmount: 500,
          status: transactionStatus.rejected1,
          billable: false,
        },
      ],
      // eslint-disable-next-line @typescript-eslint/naming-convention
      RFM: [
        {
          platform: "RFM",
          totalFinalAmount: 1000,
          billable: true,
        },
      ],
    });

    expect(spyFetchSqlData).toHaveBeenCalledWith(
      `
      SELECT
       A.[CREATED_DATE]    AS Created_Date,
       A.[UPDATED_DATE]    AS Updated_Date,
       A.[SERVICE_NUMBER]  AS Service_Number,
       A.[ORIGINAL_AMT]    AS Original_Amt,
       A.[REFUND_AMT]      AS Refund_Amt,
       A.[FINAL_AMT]       AS Final_Amt,
       A.[CURRENCY]        AS Currency,
       A.[COUNTRY]         AS Country,
       A.[BILLABLE]        AS Billable,
       A.[PLATFORM]        AS Platform,
       A.[CUST_NUMBER]     AS Cust_Number,
       A.[RCODE]           AS Rcode,
       A.[INTEGRATOR_NAME] AS Integrator_Name,
       A.[PROGRAM_NAME]    AS Program_Name,
       A.[BILLING_NAME]    AS Billing_Name,
       A.[TRANSACTION_ID]  AS Transaction_ID,
       A.[RECEIPT_ID]      AS Receipt_ID,
       A.[INTERAC_REF]     AS Interac_Ref,
       A.[FI_NAME]         AS FI_Name,
       A.[STATUS]          AS Status
        FROM [transactions] A
        WHERE A.[SERVICE_NUMBER] IN ('1234567890')
        AND 
        (updatedat BETWEEN  
        CreateDate(2023,01,01) AND CreateDate(2023,12,31)
        OR createdat BETWEEN  
        CreateDate(2023,01,01) AND CreateDate(2023,12,31)
      )
   `,
      "abc"
    );

    expect(spyGetAuthToken).toHaveBeenCalledTimes(1);
  });
});

describe("getFirstTransactionEver", () => {
  it("should fetch the first transaction date for a service number", async () => {
    const spyGetFirstTransactionDateSql = vi.spyOn(
      getFirstTransactionDateSql,
      "getFirstTransactionDateSql"
    );
    const spyFetchSqlData = vi
      .spyOn(fetchSqlData, "fetchSqlData")
      .mockResolvedValue([
        {
          firstTransaction: "2023-01-01",
          serviceNumber: "9999999999",
        },
      ]);

    const result = await getFirstTransactionEver("9999999999", "token");

    expect(result).toEqual({
      year: 2023,
      month: 1,
      day: 1,
    });

    expect(spyGetFirstTransactionDateSql).toHaveBeenCalledWith("9999999999");
    expect(spyFetchSqlData).toHaveBeenCalledWith(
      `
    SELECT
        LEFT(ToString(ToDatetime(MIN(f.[CREATED_DATE]))),10) as FirstTransaction,
        f.[SERVICE_NUMBER] as ServiceNumber
    FROM[transactions] f
    WHERE f.[SERVICE_NUMBER] in ('9999999999')
    AND STATUS = 'STATUS_SUCCESS'
    GROUP BY F.[SERVICE_NUMBER]
   `,
      "token"
    );
  });
});
