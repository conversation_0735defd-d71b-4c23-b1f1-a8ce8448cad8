import {
  isWithinInterval,
  startOfDay,
  endOfDay,
  startOfMonth,
  endOfMonth,
  subMonths,
  format,
  subDays,
} from "date-fns";

type DateOnly = {
  year: number;
  month: number;
  day: number;
};

function toDateOnly(date: Date): DateOnly {
  return {
    year: date.getFullYear(),
    month: date.getMonth() + 1,
    day: date.getDate(),
  };
}

function formatDateOnlyWithCommas(date: DateOnly): string {
  return `${date.year},${String(date.month).padStart(2, "0")},${String(
    date.day
  ).padStart(2, "0")}`;
}

function formatDateWithCommas(date: Date): string {
  return `${date.getUTCFullYear()},${String(date.getUTCMonth() + 1).padStart(2, "0")},${String(
    date.getUTCDate()
  ).padStart(2, "0")}`;
}

function toDateOnlyFromString(dateString: string): DateOnly {
  return toDateOnly(new Date(dateString));
}

function fromDateOnly(dateOnly: DateOnly): Date {
  return new Date(dateOnly.year, dateOnly.month - 1, dateOnly.day);
}

function fromDateOnlyToString(dateOnly: DateOnly): string {
  return format(fromDateOnly(dateOnly), "yyyy-MM-dd");
}

function isSameDay(date1: DateOnly, date2: DateOnly): boolean {
  return (
    date1.year === date2.year &&
    date1.month === date2.month &&
    date1.day === date2.day
  );
}

function isDayBefore(firstDate: DateOnly, secondDate: DateOnly): boolean {
  const date1NextDay = fromDateOnly(firstDate);
  date1NextDay.setDate(date1NextDay.getDate() + 1);
  const nextDayDateOnly = {
    year: date1NextDay.getFullYear(),
    month: date1NextDay.getMonth() + 1,
    day: date1NextDay.getDate(),
  };

  return isSameDay(nextDayDateOnly, secondDate);
}

function isDateBeforeOrEqual(
  firstDate: DateOnly,
  secondDate: DateOnly
): boolean {
  return (
    fromDateOnly(firstDate).getTime() <= fromDateOnly(secondDate).getTime()
  );
}

function getHigherDate(firstDate: DateOnly, secondDate: DateOnly): DateOnly {
  const firstDateObject = fromDateOnly(firstDate);
  const secondDateObject = fromDateOnly(secondDate);

  return firstDateObject.getTime() > secondDateObject.getTime()
    ? firstDate
    : secondDate;
}

function getLowerDate(firstDate: DateOnly, secondDate: DateOnly): DateOnly {
  const firstDateObject = fromDateOnly(firstDate);
  const secondDateObject = fromDateOnly(secondDate);

  return firstDateObject.getTime() < secondDateObject.getTime()
    ? firstDate
    : secondDate;
}

function isDateInRange(
  date: DateOnly,
  startDate: DateOnly,
  endDate: DateOnly
): boolean {
  const dateToCheck = fromDateOnly(date);
  const start = startOfDay(fromDateOnly(startDate));
  const end = endOfDay(fromDateOnly(endDate));

  return isWithinInterval(dateToCheck, { start, end });
}

function startOfDayOnly(date: DateOnly): Date {
  return startOfDay(fromDateOnly(date));
}

function endOfDayOnly(date: DateOnly): Date {
  return endOfDay(fromDateOnly(date));
}

function startOfTheMonthOnly(date: DateOnly): DateOnly {
  const startOfMonthDate = startOfMonth(fromDateOnly(date));

  return toDateOnly(startOfMonthDate);
}

function endOfTheMonthOnly(date: DateOnly): DateOnly {
  const endOfMonthDate = endOfMonth(fromDateOnly(date));

  return toDateOnly(endOfMonthDate);
}

function startOfPreviousMonthOnly(date: DateOnly): DateOnly {
  const previousMonthDate = subMonths(fromDateOnly(date), 1);
  const startOfPreviousMonthDate = startOfMonth(previousMonthDate);

  return toDateOnly(startOfPreviousMonthDate);
}

function endOfPreviousMonthOnly(date: DateOnly): DateOnly {
  const previousMonthDate = subMonths(fromDateOnly(date), 1);
  const endOfPreviousMonthDate = endOfMonth(previousMonthDate);

  return toDateOnly(endOfPreviousMonthDate);
}

function subMonthsOnly(dateOnly: DateOnly, months: number): DateOnly {
  const date = fromDateOnly(dateOnly);
  const subtractedDate = subMonths(date, months);

  return toDateOnly(subtractedDate);
}

function subDaysOnly(dateOnly: DateOnly, days: number): DateOnly {
  const date = fromDateOnly(dateOnly);
  const subtractedDate = subDays(date, days);

  return toDateOnly(subtractedDate);
}

/**
 * Converts the given Date to a string in yyyy-MM-dd format.
 *
 * @param date - The Date object to convert.
 * @returns String of the date in yyyy-MM-dd format.
 */
function dateToString(date: Date): string {
  return format(date, "yyyy-MM-dd");
}

export {
  toDateOnly,
  toDateOnlyFromString,
  fromDateOnly,
  fromDateOnlyToString,
  type DateOnly,
  isDateInRange,
  isSameDay,
  isDayBefore,
  isDateBeforeOrEqual,
  getHigherDate,
  getLowerDate,
  startOfDayOnly,
  endOfDayOnly,
  startOfTheMonthOnly,
  endOfTheMonthOnly,
  startOfPreviousMonthOnly,
  endOfPreviousMonthOnly,
  subMonthsOnly,
  subDaysOnly,
  dateToString,
  formatDateOnlyWithCommas,
  formatDateWithCommas,
};
