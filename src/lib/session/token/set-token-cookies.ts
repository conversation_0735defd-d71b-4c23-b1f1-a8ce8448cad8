import { randomUUID } from "node:crypto";

import { environment } from "@constants/environment/index.js";

import { type UserProfile } from "../../../types/user-profile";

import type { FastifyReply } from "fastify";

const setTokenCookies = async (
  reply: FastifyReply,
  userProfile: UserProfile
) => {
  const nounce = randomUUID();
  const token = await createAccessToken(reply, userProfile, nounce);
  const refreshToken = await createRefreshToken(reply, userProfile, nounce);
  void reply.setCookie(environment.accessTokenCookieName, token, {
    httpOnly: true,
    sameSite: "strict",
    secure: environment.isProduction,
    signed: environment.isProduction,
    path: "/",
  });

  void reply.setCookie(environment.refreshTokenCookieName, refreshToken, {
    httpOnly: true,
    sameSite: "strict",
    secure: environment.isProduction,
    signed: environment.isProduction,
    path: "/",
  });
};

const createRefreshToken = async (
  reply: FastifyReply,
  userProfile: UserProfile,
  nounce: string
): Promise<string> =>
  reply.refreshJwtSign(
    { userProfile: { id: userProfile.id }, nounce },
    { expiresIn: environment.refreshTokenDuration }
  );

const createAccessToken = async (
  reply: FastifyReply,
  userProfile: UserProfile,
  nounce: string
): Promise<string> =>
  reply.accessJwtSign(
    { userProfile: { id: userProfile.id }, accessKey: true, nounce },
    { expiresIn: environment.accessTokenDuration }
  );

export { setTokenCookies };
