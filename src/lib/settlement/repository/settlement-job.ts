import { type PrismaClient, type Prisma } from "@prisma/client";

const createMonthlySettlementJob = async (
  month: number,
  year: number,
  userId: number,
  prisma: PrismaClient
): Promise<number> => {
  const job = await prisma.monthlyAllSettlementJob.create({
    data: {
      month,
      year,
      generatedById: userId,
    },
  });

  return job.monthlyAllSettlementJobId;
};

const updateMonthlySettlementJob = async (
  jobId: number,
  meta:
    | {
        totalCount: number;
        successCount: number;
        errorCount: number;
        skippedCount: number;
        timeTakenInSeconds: number;
      }
    | { errorMessage: string; timeTakenInSeconds: number },
  status: "PROGRESS" | "SUCCESS" | "ERROR",
  prisma: PrismaClient
): Promise<number> => {
  const job = await prisma.monthlyAllSettlementJob.update({
    where: {
      monthlyAllSettlementJobId: jobId,
    },
    data: {
      status,
      meta,
    },
  });

  return job.monthlyAllSettlementJobId;
};

const getMonthlySettlementJob = async (
  month: number,
  year: number,
  prisma: PrismaClient
): Promise<
  | {
      month: number;
      year: number;
      status: string;
      userId: number;
      userName: string;
    }
  | undefined
> => {
  const job = await prisma.monthlyAllSettlementJob.findFirst({
    where: {
      month,
      year,
    },
    select: {
      month: true,
      year: true,
      status: true,
      createdAt: true,
      user: {
        select: {
          userId: true,
          userName: true,
        },
      },
    },
  });

  if (!job) {
    return;
  }

  return {
    month: job.month,
    year: job.year,
    status: job.status,
    userId: job.user.userId,
    userName: job.user.userName,
  };
};

const getAllMonthlySettlementJob = async (
  prisma: PrismaClient,
  offset = 0,
  limit = 20
): Promise<
  | {
      jobs: Array<{
        jobId: number;
        month: number;
        year: number;
        status: string;
        userId: number;
        userName: string;
        generatedAt: Date;
        meta: Prisma.JsonValue;
      }>;
      totalCount: number;
    }
  | undefined
> => {
  const [jobs, totalCount] = await prisma.$transaction(async (tx) => {
    const jobs = await tx.monthlyAllSettlementJob.findMany({
      where: {
        deletedAt: null,
      },
      skip: offset,
      take: limit,
      select: {
        monthlyAllSettlementJobId: true,
        month: true,
        year: true,
        status: true,
        createdAt: true,
        user: {
          select: {
            userId: true,
            userName: true,
          },
        },
        meta: true,
      },
    });

    const count = await tx.monthlyAllSettlementJob.count({
      where: {
        deletedAt: null,
      },
    });

    return [jobs, count];
  });

  if (!jobs) {
    return;
  }

  const flattenedResult = jobs.map((job) => ({
    jobId: job.monthlyAllSettlementJobId,
    month: job.month,
    year: job.year,
    status: job.status,
    userId: job.user.userId,
    userName: job.user.userName,
    generatedAt: job.createdAt!,
    meta: job.meta,
  }));

  return {
    jobs: flattenedResult,
    totalCount,
  };
};

const getMonthlySettlementJobInProgress = async (
  prisma: PrismaClient
): Promise<
  | {
      month: number;
      year: number;
      startedAt: Date;
      userId: number;
      userName: string;
    }
  | undefined
> => {
  const job = await prisma.monthlyAllSettlementJob.findFirst({
    where: {
      status: "PROGRESS",
    },
    select: {
      month: true,
      year: true,
      createdAt: true,
      user: {
        select: {
          userId: true,
          userName: true,
        },
      },
    },
  });

  if (!job) {
    return;
  }

  return {
    month: job.month,
    year: job.year,
    startedAt: job.createdAt!,
    userId: job.user.userId,
    userName: job.user.userName,
  };
};

const deleteMonthlySettlementJobs = async (
  jobIds: number[],
  prisma: PrismaClient
) => {
  await prisma.$transaction(async (tx) => {
    await tx.monthlyPlatformSettlement.deleteMany({
      where: {
        monthlyCustomerSettlement: {
          jobId: {
            in: jobIds,
          },
        },
      },
    });

    await tx.monthlyCustomerSettlement.deleteMany({
      where: {
        jobId: {
          in: jobIds,
        },
      },
    });

    await tx.monthlyAllSettlementJob.deleteMany({
      where: {
        monthlyAllSettlementJobId: {
          in: jobIds,
        },
      },
    });
  });
};

export {
  createMonthlySettlementJob,
  getMonthlySettlementJobInProgress,
  getMonthlySettlementJob,
  updateMonthlySettlementJob,
  getAllMonthlySettlementJob,
  deleteMonthlySettlementJobs,
};
