import { type Prisma } from "@prisma/client";

import { type selectClause } from "./select-clause";
import { toDateOnly } from "../../../../utils/date-only";
import {
  type MerchantPlatformMember,
  type CustomerTypeMember,
  type MerchantSettlementConfiguration,
  type NonMerchantPlatformConfiguration,
  type TierItem,
  type TierSet,
} from "../types";

const flattenMerchantConfiguration = (
  configuration: Prisma.customerCustomerTypeGetPayload<
    ReturnType<typeof selectClause>
  >
): MerchantSettlementConfiguration => {
  const customerTypeMember = mapCustomerTypeMember(configuration);

  // Group merchant platform members using the nested merchant platform array.
  const merchantPlatforms = groupMerchantPlatformMembers(
    configuration.customer
      .merchantPlatform_merchantPlatform_clientCustomerIdTocustomer as Array<
      RawPlatformItem & Record<string, unknown>
    >
  );

  return {
    customerCustomerTypeId: configuration.customerCustomerTypeId,
    customerId: configuration.customer.customerId,
    serviceNumber: configuration.customer.serviceNumber,
    customerName: configuration.customer.customerName,
    customerTradingName: configuration.customer.customerTradingName ?? "",
    settlementType:
      configuration.customer.customerSettlementType?.description ?? undefined,
    firstTransactionDate: configuration.customer.firstTransactionDate
      ? toDateOnly(new Date(configuration.customer.firstTransactionDate))
      : undefined,
    isCombineMultipleServices: Boolean(
      configuration.customer.multipleServiceNoTierSet
    ),
    isCombineMultipleServicesKyc: Boolean(
      configuration.customer.multipleServiceNoTierSetKyc
    ),
    customerType: customerTypeMember,
    platformConfigurations: merchantPlatforms,
  };
};

/**
 * Type alias for a raw platform item from the nested Prisma query.
 */
type RawPlatformItem = NonNullable<
  Prisma.customerCustomerTypeGetPayload<
    ReturnType<typeof selectClause>
  >["customer"]["merchantPlatform_merchantPlatform_clientCustomerIdTocustomer"]
>[number];

/**
 * Maps a single tier item from Prisma to our TierItem type.
 */
function mapTierItem(
  tierItem: Prisma.tierItemGetPayload<Record<string, unknown>>
): TierItem {
  return {
    id: tierItem.tierItemId,
    maxAmount: Number(tierItem.maxAmount),
    salesFee: Number(tierItem.salesFee),
    minAmount: Number(tierItem.minAmount),
    transactionFee: tierItem.transactionFee
      ? Number(tierItem.transactionFee)
      : 0,
  };
}

/**
 * Maps a tier set from Prisma to our TierSet type.
 *
 * We require that the passed tierSet has the properties we need.
 * The caller may override which key holds the tier items.
 */
function mapTierSet<
  T extends { tierSetId: number; tierSetName: string } & Partial<
    Record<K, unknown>
  >,
  K extends string = "tierItem",
>(tierSet: T, key?: K): TierSet {
  // Default to "tierItem" if key is not provided.
  const effectiveKey = (key ?? "tierItem") as K;
  const rawTierItems = (tierSet as Record<string, unknown>)[effectiveKey];
  const items: TierItem[] = Array.isArray(rawTierItems)
    ? (
        rawTierItems as unknown as Array<
          Prisma.tierItemGetPayload<Record<string, unknown>>
        >
      ).map((element) => mapTierItem(element))
    : [];

  return {
    id: tierSet.tierSetId,
    name: tierSet.tierSetName,
    tierItem: items,
  };
}

/**
 * Generic mapper for non‑merchant configurations (integrator, agent, subAgent).
 * Uses the provided prefix to look up fields.
 */
function mapNonMerchantConfiguration(
  item: RawPlatformItem & Record<string, unknown>,
  prefix: "integrator" | "agent" | "subAgent"
): NonMerchantPlatformConfiguration {
  const customerIdField = `${prefix}CustomerId`;
  const gatewayFeeField = `${prefix}GatewayFee`;
  const cumulativeField = `${prefix}CumulativeOrSplit`;
  const tierSetField = `tierSet_merchantPlatform_${prefix}TierSetIdTotierSet`;
  const saleTierSetField = `tierSet_merchantPlatform_${prefix}SaleTierSetIdTotierSet`;

  const nonMerchantId = item[customerIdField];

  if (!nonMerchantId) {
    // Return a default configuration if the field is missing.
    return {
      nonMerchantId: 0,
      gatewayFeePercentage: 0,
      isCumulative: false,
      tierSet: { id: 0, name: "", tierItem: [] },
      saleTierSet: { id: 0, name: "", tierItem: [] },
    };
  }

  return {
    nonMerchantId: Number(nonMerchantId),
    gatewayFeePercentage: Number(item[gatewayFeeField]),
    isCumulative: item[cumulativeField] === "c",
    tierSet: item[tierSetField]
      ? mapTierSet(
          item[tierSetField] as {
            tierSetId: number;
            tierSetName: string;
            tierItem?: unknown;
          }
        )
      : { id: 0, name: "", tierItem: [] },
    saleTierSet: item[saleTierSetField]
      ? mapTierSet(
          item[saleTierSetField] as {
            tierSetId: number;
            tierSetName: string;
            tierItem?: unknown;
          }
        )
      : { id: 0, name: "", tierItem: [] },
  };
}

/**
 * Maps a raw merchant platform item into a MerchantPlatformMember.
 */
function mapMerchantPlatformMember(
  item: RawPlatformItem & Record<string, unknown>
): MerchantPlatformMember {
  // Normalize platform code.
  const platformCode = item.platform.platformCode
    .toUpperCase()
    .replaceAll(/\s+/g, "");

  // Map client and rejected tier sets.
  const clientTierSet = item.tierSet_merchantPlatform_clientTierSetIdTotierSet;
  const rejectedTierSet =
    item.tierSet_merchantPlatform_rejectOneTierSetIdTotierSet;

  const member: MerchantPlatformMember = {
    id: item.merchantPlatformId,
    delayInMonths: item.clientDelayMonths,
    transactionFee: Number(item.clientTransactionFee),
    gatewayFee: Number(item.clientGatewayFee),
    refundFee: Number(item.clientRefundFee),
    hasMinimum: Boolean(item.hasMinimum),
    minimumThreshold: Number(item.minimumThreshold),
    minimumCharge: Number(item.minimumCharge),
    fromDate: item.fromDate ? toDateOnly(item.fromDate) : undefined,
    toDate: item.toDate ? toDateOnly(item.toDate) : undefined,
    platformId: item.platform.platformId,
    platformName: item.platform.platformName,
    platformCode,
    platformType: item.platform.paymentType?.paymentTypeName ?? "",
    tierSet: clientTierSet
      ? mapTierSet(
          clientTierSet as {
            tierSetId: number;
            tierSetName: string;
            tierItem?: unknown;
          }
        )
      : { id: 0, name: "", tierItem: [] },
    delayTierItem: item.tierItem ? mapTierItem(item.tierItem) : undefined,
  };

  if (rejectedTierSet) {
    member.rejected1TierSet = mapTierSet(
      rejectedTierSet as {
        tierSetId: number;
        tierSetName: string;
        tierItem?: unknown;
      }
    );
  }

  // Map non‑merchant configurations for integrator, agent, and subAgent.
  const integratorConfig = mapNonMerchantConfiguration(item, "integrator");
  const agentConfig = mapNonMerchantConfiguration(item, "agent");
  const subAgentConfig = mapNonMerchantConfiguration(item, "subAgent");

  if (integratorConfig.nonMerchantId) {
    member.integrator = integratorConfig;
  }

  if (agentConfig.nonMerchantId) {
    member.agent = agentConfig;
  }

  if (subAgentConfig.nonMerchantId) {
    member.subAgent = subAgentConfig;
  }

  return member;
}

/**
 * Groups merchant platform members by their normalized platform code.
 */
function groupMerchantPlatformMembers(
  items: Array<RawPlatformItem & Record<string, unknown>>
): Record<string, MerchantPlatformMember[]> {
  const grouped: Record<string, MerchantPlatformMember[]> = {};

  for (const item of items) {
    const member = mapMerchantPlatformMember(item);
    const code = member.platformCode;

    grouped[code] ||= [];
    grouped[code]!.push(member);
  }

  return grouped;
}

/**
 * Maps customer type details using the top‑level fields from the select clause.
 */
function mapCustomerTypeMember(
  configuration: Prisma.customerCustomerTypeGetPayload<
    ReturnType<typeof selectClause>
  >
): CustomerTypeMember {
  return {
    id: configuration.customerCustomerTypeId,
    isCombineIdpTierSet: Boolean(configuration.includeIDPTierSet),
    isCombineAchTierSet: Boolean(configuration.includeACHTierSet),
    isCombineRtoTierSet: Boolean(configuration.includeRTOTierSet),
    statementFolderLocation: configuration.statementFolderLocation ?? undefined,
    statementFrequencyId:
      configuration.statementFrequency?.statementFrequencyId ?? undefined,
    statementFrequencyName:
      configuration.statementFrequency?.statementFrequencyName ?? undefined,
    statementFrequencyCode:
      configuration.statementFrequency?.statementFrequencyCode ?? undefined,
    serviceNumbers:
      configuration?.customerCustomerTypeServiceNumber?.map((service) => ({
        serviceNumber: service.serviceNumber!,
        fromDate: service.fromDate ? toDateOnly(service.fromDate) : undefined,
        toDate: service.toDate ? toDateOnly(service.toDate) : undefined,
      })) ?? [],
    customerTypeId: configuration.customerType.customerTypeId,
    customerTypeName: configuration.customerType.customerTypeName,
    volumeCombination: configuration.volumeCombination,
  };
}

export { flattenMerchantConfiguration };
