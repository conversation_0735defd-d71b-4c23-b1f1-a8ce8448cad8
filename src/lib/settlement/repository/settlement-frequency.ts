/* eslint-disable @typescript-eslint/naming-convention */

import { kycClientPlatformId, statusInit } from "@constants/settlement";
import { type Customer } from "@lib/customer/repository/types";
import { getCustomerIdByCustomerCustomerType } from "@lib/customer-customer-type/repository";
import {
  type FrequencySettlementSchedule,
  type FrequencySettlementsResult,
} from "@lib/settlement/repository/types";

import { type DateOnly, fromDateOnly } from "../../../utils/date-only";
import {
  type KycRecord,
  type SettlementResult,
} from "../functions/workers/types";

import type { Prisma, PrismaClient } from "@prisma/client";

const saveFrequencySettlements = async (
  settlements: FrequencySettlementsResult,
  fromDate: DateOnly,
  toDate: DateOnly,
  prisma: PrismaClient
): Promise<void> => {
  for (const [customerCustomerTypeId, result] of Object.entries(settlements)) {
    const merchantSettlementResult = result.data?.result;

    const fromDateString = fromDateOnly(fromDate);
    const toDateString = fromDateOnly(toDate);

    // eslint-disable-next-line no-await-in-loop
    const customerId = await getCustomerIdByCustomerCustomerType(
      Number(customerCustomerTypeId),
      prisma
    );

    if (!customerId) {
      throw new Error(
        `Customer not found for customerCustomerTypeId ${customerCustomerTypeId}`
      );
    }

    // eslint-disable-next-line no-await-in-loop
    await prisma.$transaction(
      async (transactionPrisma) => {
        await _updateFrequencySettlementSchedule(
          result.scheduleId,
          result.status === "SUCCESS" ? "COMPLETE" : result.status,
          transactionPrisma,
          result?.message
        );

        if (merchantSettlementResult?.settlement) {
          await _saveFrequencyPlatformSettlements(
            {
              customerId,
              customerCustomerTypeId: Number(customerCustomerTypeId),
            },
            { fromDate, toDate },
            {
              merchantSettlement: merchantSettlementResult,
              endBalance: result.data?.endBalance ?? 0,
            },
            transactionPrisma
          );
        }

        if (merchantSettlementResult?.kyc) {
          const kycRecordsArray = Object.values(merchantSettlementResult.kyc);

          const aggregatedTransactionCount: number = kycRecordsArray.reduce(
            (sum: number, record) => sum + (record.transactionCount || 0),
            0
          );
          const aggregatedTransactionAmount: number = kycRecordsArray.reduce(
            (sum: number, record) => sum + (record.totalTransactionAmount || 0),
            0
          );

          const kycSettlement =
            await transactionPrisma.customerSettlements.upsert({
              where: {
                customerId_platformId_fromDate_toDate: {
                  customerId,
                  platformId: kycClientPlatformId,
                  fromDate: fromDateString,
                  toDate: toDateString,
                },
              },
              update: {
                customerCustomerTypeId: Number(customerCustomerTypeId),
                status: statusInit,
                transactionCount: aggregatedTransactionCount,
                totalTransactionAmount: aggregatedTransactionAmount,
              },
              create: {
                customerId,
                platformId: kycClientPlatformId,
                fromDate: fromDateString,
                toDate: toDateString,
                customerCustomerTypeId: Number(customerCustomerTypeId),
                status: statusInit,
                transactionCount: aggregatedTransactionCount,
                totalTransactionAmount: aggregatedTransactionAmount,
              },
            });

          await upsertSettlementKycRecords(
            transactionPrisma,
            kycSettlement.customerSettlementsId,
            kycRecordsArray
          );
        }
      },
      { timeout: 50_000, maxWait: 10_000 }
    );
  }
};

async function upsertSettlementKycRecords(
  tx: Prisma.TransactionClient,
  customerSettlementsId: number,
  kycRecords: KycRecord[]
): Promise<void> {
  // Update or create:
  await Promise.all(
    kycRecords.map(async ({ kycTypeId, ...data }) => {
      const { count } = await tx.customerSettlementKyc.updateMany({
        where: { customerSettlementsId, kycTypeId },
        data,
      });

      if (count === 0) {
        await tx.customerSettlementKyc.create({
          data: { customerSettlementsId, kycTypeId, ...data },
        });
      }
    })
  );

  // Delete didn't include types
  const incomingTypeIds = kycRecords.map((r) => r.kycTypeId);
  await tx.customerSettlementKyc.deleteMany({
    where: {
      customerSettlementsId,
      kycTypeId: { notIn: incomingTypeIds },
    },
  });
}

const getFrequencyPlatformSettlement = async (
  customerId: number,
  platformId: number,
  toDate: DateOnly,
  prisma: PrismaClient
): Promise<{ settlementId: number; endBalance: number } | undefined> => {
  const platformSettlement = await prisma.customerSettlements.findFirst({
    where: {
      customerId,
      platformId,
      toDate: fromDateOnly(toDate),
    },
  });

  if (!platformSettlement) {
    return;
  }

  return {
    settlementId: platformSettlement.customerSettlementsId,
    endBalance: platformSettlement.endBalance?.toNumber() ?? 0,
  };
};

const createFrequencySettlementSchedule = async (
  customer: Customer,
  fromDate: DateOnly,
  toDate: DateOnly,
  prisma: PrismaClient
): Promise<FrequencySettlementSchedule> => {
  const currentDate = new Date();

  const schedule = await prisma.customerSettlementGenerationSchedule.create({
    data: {
      serviceNumber: customer.serviceNumber,
      fromDate: fromDateOnly(fromDate),
      toDate: fromDateOnly(toDate),
      generationDate: currentDate,
      startDate: currentDate,
      generationType: "INITIAL",
      generationStatus: "STARTED",
      customerCustomerTypeId: customer.customerCustomerTypeId,
    },
  });

  return {
    scheduleId: schedule.customerSettlementGenerationScheduleId,
    generationType:
      schedule.generationType as FrequencySettlementSchedule["generationType"],
    generationStatus:
      schedule.generationStatus as FrequencySettlementSchedule["generationStatus"],
  };
};

const getFrequencySettlementSchedule = async (
  customerCustomerTypeId: number,
  fromDate: Date,
  toDate: Date,
  prisma: PrismaClient | Prisma.TransactionClient
): Promise<FrequencySettlementSchedule | undefined> => {
  const schedule = await prisma.customerSettlementGenerationSchedule.findFirst({
    where: {
      customerCustomerTypeId,
      fromDate,
      toDate,
    },
    orderBy: {
      updatedAt: "desc",
    },
  });

  if (!schedule) {
    return;
  }

  return {
    scheduleId: schedule.customerSettlementGenerationScheduleId,
    generationType:
      schedule.generationType as FrequencySettlementSchedule["generationType"],
    generationStatus:
      schedule.generationStatus as FrequencySettlementSchedule["generationStatus"],
    ...(schedule.errorMessage && {
      errorMessage: schedule.errorMessage,
    }),
  };
};

const _updateFrequencySettlementSchedule = async (
  scheduleId: number,
  status: "COMPLETE" | "SKIPPED" | "ERROR",
  prisma: Prisma.TransactionClient,
  errorMessage?: string
): Promise<void> => {
  await prisma.customerSettlementGenerationSchedule.update({
    where: {
      customerSettlementGenerationScheduleId: scheduleId,
    },
    data: {
      completionDate: new Date(),
      generationStatus: status,
      errorMessage: errorMessage ?? null,
    },
  });
};

const _saveFrequencyPlatformSettlements = async (
  customer: { customerId: number; customerCustomerTypeId: number },
  period: { fromDate: DateOnly; toDate: DateOnly },
  data: { merchantSettlement: SettlementResult; endBalance: number },
  transactionPrisma: Prisma.TransactionClient
): Promise<void> => {
  const fromDate = fromDateOnly(period.fromDate);
  const toDate = fromDateOnly(period.toDate);

  const platformSettlements = Object.entries(
    data.merchantSettlement.settlement
  ).map(async ([platformCode, platformSettlement]) => {
    const { totalChargedFees, feesBreakdown } = platformSettlement;

    if (!feesBreakdown) {
      throw new Error(
        `Fees breakdown is missing for ${platformCode} platform settlement`
      );
    }

    const totalFees = {
      gatewayFee: totalChargedFees.gatewayFeeTotal,
      transactionFee:
        totalChargedFees.transactionFeeTotal +
        totalChargedFees.rejected1FeeTotal,
      salesFee: totalChargedFees.salesFeeTotal,
      refundFee: totalChargedFees.refundFeeTotal,
      minimumFeeTotal: totalChargedFees.minimumFeeTotal,
    };

    // Rename the keys to match the database column names
    const transactionSummary = {
      transactionCount:
        platformSettlement.totalTransactionSummary.transactionCount,
      totalTransactionAmount:
        platformSettlement.totalTransactionSummary.totalTransactionAmount,
      refundCount: platformSettlement.totalTransactionSummary.refundCount,
      totalRefundAmount:
        platformSettlement.totalTransactionSummary.totalRefundAmount,
      totalFailedAmount:
        platformSettlement.totalTransactionSummary.totalFailedAmount,
      total2FaRejectCount:
        platformSettlement.totalTransactionSummary.rejected1Count,
      total2FaRejectAmount:
        platformSettlement.totalTransactionSummary.totalRejected1Amount,
      txnCountETI_R1: platformSettlement.totalTransactionSummary._RCount,
      txnAmountRTO_R: platformSettlement.totalTransactionSummary.total_RAmount,
      minimumFeeCount:
        platformSettlement.totalTransactionSummary.minimumAmountCount,
      totalMinimumAmount:
        platformSettlement.totalTransactionSummary.totalMinimumAmount,
      partialReturnAmountRTO:
        platformSettlement.totalTransactionSummary.totalPartialReturnAmount,
      partialReturnCountRTO:
        platformSettlement.totalTransactionSummary.partialReturnCount,
    };

    return transactionPrisma.customerSettlements.upsert({
      where: {
        customerId_platformId_fromDate_toDate: {
          customerId: customer.customerId,
          platformId: platformSettlement.platformId,
          fromDate,
          toDate,
        },
      },
      update: {
        customerCustomerTypeId: customer.customerCustomerTypeId,
        status: statusInit,
        ...totalFees,
        ...transactionSummary,
        meta: { feesBreakdown },
      },
      create: {
        customerId: customer.customerId,
        platformId: platformSettlement.platformId,
        fromDate,
        toDate,
        customerCustomerTypeId: customer.customerCustomerTypeId,
        status: statusInit,
        ...totalFees,
        ...transactionSummary,
        meta: { feesBreakdown },
      },
    });
  });

  // Overall summary - required for old FA to display the settlement
  platformSettlements.push(
    transactionPrisma.customerSettlements.upsert({
      where: {
        customerId_platformId_fromDate_toDate: {
          customerId: customer.customerId,
          platformId: 1,
          fromDate,
          toDate,
        },
      },
      update: {
        customerCustomerTypeId: customer.customerCustomerTypeId,
        status: statusInit,
        endBalance: data.endBalance,
      },
      create: {
        customerId: customer.customerId,
        platformId: 1,
        fromDate,
        toDate,
        customerCustomerTypeId: customer.customerCustomerTypeId,
        status: statusInit,
        endBalance: data.endBalance,
      },
    })
  );

  await Promise.all(platformSettlements);
};

export {
  saveFrequencySettlements,
  getFrequencyPlatformSettlement,
  createFrequencySettlementSchedule,
  getFrequencySettlementSchedule,
};
