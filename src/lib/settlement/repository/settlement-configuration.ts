import { getAssociatedMerchantIdsForNonMerchant } from "@lib/customer/repository/get-customer";
import { type PrismaClient } from "@prisma/client";

import { flattenMerchantConfiguration } from "./helpers/flatten-merchant-configuration";
import { selectClause } from "./helpers/select-clause";
import {
  type MerchantSettlementConfiguration,
  type NonMerchantSettlementConfiguration,
  type NonMerchantType,
  nonMerchantTypes,
} from "./types";
import { type DateOnly, fromDateOnly } from "../../../utils/date-only";

const getMerchantsSettlementConfiguration = async (
  merchantIds: number[],
  effectiveDate: { fromDate: DateOnly; toDate: DateOnly },
  prisma: PrismaClient
): Promise<MerchantSettlementConfiguration[]> => {
  if (merchantIds?.length === 0) {
    return [];
  }

  const customersWithSettlementConfigs =
    await prisma.customerCustomerType.findMany({
      where: {
        customerId: { in: merchantIds },
        deletedAt: null,
        customerType: {
          customerTypeName: "Merchant",
        },
      },
      ...selectClause(
        fromDateOnly(effectiveDate.fromDate),
        fromDateOnly(effectiveDate.toDate)
      ),
    });

  return customersWithSettlementConfigs.map((config) =>
    flattenMerchantConfiguration(config)
  );
};

const getNonMerchantsSettlementConfiguration = async (
  nonMerchantId: number,
  effectiveDate: { fromDate: DateOnly; toDate: DateOnly },
  prisma: PrismaClient
): Promise<NonMerchantSettlementConfiguration> => {
  const customersWithSettlementConfigs =
    await prisma.customerCustomerType.findFirst({
      where: {
        customerId: nonMerchantId,
        deletedAt: null,
        customerType: {
          customerTypeName: { not: "Merchant" },
        },
      },
      ...selectClause(
        fromDateOnly(effectiveDate.fromDate),
        fromDateOnly(effectiveDate.toDate)
      ),
    });

  if (!customersWithSettlementConfigs) {
    throw new Error(
      `No settlement configuration found for customer ${nonMerchantId}.`
    );
  }

  // Flatten the configuration into our internal type.
  const config = flattenMerchantConfiguration(customersWithSettlementConfigs);
  const nonMerchantTypeName = config.customerType.customerTypeName;

  if (!nonMerchantTypes.includes(nonMerchantTypeName as NonMerchantType)) {
    throw new Error(
      `Invalid non-merchant type: ${nonMerchantTypeName}. Expected one of ${nonMerchantTypes.join(", ")}.`
    );
  }

  // Retrieve the associated merchant IDs for this non-merchant.
  const associatedMerchantsSet = await getAssociatedMerchantIdsForNonMerchant(
    nonMerchantId,
    nonMerchantTypeName as NonMerchantType,
    prisma
  );

  const associatedMerchantIds = [...associatedMerchantsSet];

  const nonMerchantSettlementConfiguration: NonMerchantSettlementConfiguration =
    {
      customerCustomerTypeId: config.customerCustomerTypeId,
      customerId: config.customerId,
      customerName: config.customerName,
      serviceNumber: config.serviceNumber,
      settlementType: config?.settlementType ?? "",
      isCombineMultipleServices: config.isCombineMultipleServices,
      nonMerchantType: config.customerType,
      associatedMerchantIds,
    };

  return nonMerchantSettlementConfiguration;
};

export {
  getMerchantsSettlementConfiguration,
  getNonMerchantsSettlementConfiguration,
};
