import { getAllCustomersByFrequency } from "@lib/customer/repository/get-customer";
import { getFrequency } from "@lib/frequency/repository";
import { calculateFrequencyDates } from "@lib/settlement/functions/calculate-frequency-date";
import { calculateSettlements } from "@lib/settlement/functions/calculate-settlements";
import {
  createFrequencySettlementSchedule,
  saveFrequencySettlements,
} from "@lib/settlement/repository/settlement-frequency";
import {
  createFrequencySettlementJob,
  getFrequencySettlementJobInProgress,
  getFrequencySettlementJobSuccess,
  updateFrequencySettlementJob,
} from "@lib/settlement/repository/settlement-frequency-job";
import { type FrequencySettlementsResult } from "@lib/settlement/repository/types";

import { handler } from "./handler";
import {
  endOfPreviousMonthOnly,
  startOfPreviousMonthOnly,
} from "../../../../../utils/date-only";

import type { FastifyRequest, FastifyReply } from "fastify";

vi.mock("@lib/session/user-profile/functions", () => ({
  getUserProfile: vi.fn(() => mockUser),
}));

vi.mock("@lib/customer/repository/get-customer", () => ({
  getAllCustomersByFrequency: vi.fn(),
}));

vi.mock("@lib/settlement/functions/calculate-frequency-date", () => ({
  calculateFrequencyDates: vi.fn(),
}));

vi.mock("@lib/settlement/repository/settlement-frequency", () => ({
  createFrequencySettlementSchedule: vi.fn(),
  saveFrequencySettlements: vi.fn(),
}));

vi.mock("@lib/settlement/repository/settlement-frequency-job", () => ({
  createFrequencySettlementJob: vi.fn(),
  getFrequencySettlementJobSuccess: vi.fn(),
  getFrequencySettlementJobInProgress: vi.fn(),
  updateFrequencySettlementJob: vi.fn(),
}));

vi.mock("@lib/frequency/repository", () => ({
  getFrequency: vi.fn(),
}));

vi.mock("../../../functions/calculate-settlements", () => ({
  calculateSettlements: vi.fn(),
}));

const mockRequest = {
  body: {
    frequencyId: 2,
    fromDate: "2025-01-01",
    toDate: "2025-01-15",
  },
  server: { prisma: {} },
  log: { info: vi.fn(), error: vi.fn() },
  userProfile: { id: 42 },
} as unknown as FastifyRequest<{
  Body: { frequencyId: number; fromDate: string; toDate: string };
}>;

const mockReply = {
  status: vi.fn(() => mockReply),
  send: vi.fn(),
} as unknown as FastifyReply;

const mockFrequency = {
  frequencyId: 3,
  frequencyCode: "SM",
  frequencyName: "Semi-Monthly",
};

const mockFrequencyDates = {
  fromDate: {
    year: 2025,
    month: 1,
    day: 1,
  },
  toDate: {
    year: 2025,
    month: 1,
    day: 15,
  },
};

const mockUser = {
  userProfile: {
    id: 42,
    fullName: "Test User",
  },
};

const customer1 = {
  customerId: 1,
  customerCustomerTypeId: 1,
  serviceNumber: "123",
  entityName: "Gigadat",
  entityId: 1,
  customerTradingName: "TradingName1",
  customerName: "Customer1",
  enabled: true,
  customerTypeName: "Type1",
  customerTypeId: 1,
  statementFolderLocation: "/test",
};

const customer2 = {
  customerId: 2,
  customerCustomerTypeId: 2,
  serviceNumber: "456",
  entityName: "Bitfy",
  entityId: 2,
  customerTradingName: "TradingName2",
  customerName: "Customer2",
  enabled: true,
  customerTypeName: "Type2",
  customerTypeId: 2,
  statementFolderLocation: "/test",
};

const mockSchedule1 = {
  scheduleId: 111,
  generationType: "INITIAL" as const,
  generationStatus: "STARTED" as const,
};

const mockSchedule2 = {
  scheduleId: 222,
  generationType: "INITIAL" as const,
  generationStatus: "STARTED" as const,
};

const mockSettlementsResult: FrequencySettlementsResult = {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  1: {
    status: "SUCCESS",
    scheduleId: mockSchedule1.scheduleId,
    generationType: mockSchedule1.generationType,
    generationStatus: "STARTED",
  },
  // eslint-disable-next-line @typescript-eslint/naming-convention
  2: {
    status: "ERROR",
    scheduleId: mockSchedule2.scheduleId,
    generationType: mockSchedule2.generationType,
    generationStatus: "STARTED",
  },
};

describe("Frequency Settlements Generation Handler", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should return 400 if frequency is not found", async () => {
    await handler(mockRequest, mockReply);

    expect(mockReply.status).toHaveBeenCalledWith(400);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: "Frequency is required",
      success: false,
    });
  });

  it("should return 403 if a frequency settlement job is in progress", async () => {
    vi.mocked(getFrequency).mockResolvedValue(mockFrequency);

    vi.mocked(calculateFrequencyDates).mockResolvedValue(mockFrequencyDates);

    vi.mocked(getFrequencySettlementJobInProgress).mockResolvedValue({
      jobId: 11,
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-15"),
      status: "PROGRESS",
      userId: mockUser.userProfile.id,
      userName: mockUser.userProfile.fullName,
      frequencyName: "Semi-Monthly",
      createdAt: new Date("2025-02-01"),
    });

    await handler(mockRequest, mockReply);

    expect(mockReply.status).toHaveBeenCalledWith(403);
    expect(mockReply.send).toHaveBeenCalledWith({
      success: false,
      message:
        "Frequency settlement generation is currently in progress for Semi-Monthly from 2025-01-01 to 2025-01-15.",
    });
  });

  it("should return 200 if frequency settlements have already been generated", async () => {
    vi.mocked(getFrequency).mockResolvedValue(mockFrequency);

    vi.mocked(calculateFrequencyDates).mockResolvedValue(mockFrequencyDates);

    vi.mocked(getFrequencySettlementJobSuccess).mockResolvedValue({
      jobId: 11,
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-15"),
      status: "SUCCESS",
      userId: mockUser.userProfile.id,
      userName: mockUser.userProfile.fullName,
      frequencyName: "Semi-Monthly",
      createdAt: new Date("2025-02-01"),
    });

    await handler(mockRequest, mockReply);

    expect(mockReply.status).toHaveBeenCalledWith(200);
    expect(mockReply.send).toHaveBeenCalledWith({
      success: false,
      message: "A settlement has already been generated for this period.",
    });
  });

  it("should create a frequency settlement job", async () => {
    vi.mocked(getFrequency).mockResolvedValue(mockFrequency);

    vi.mocked(calculateFrequencyDates).mockResolvedValue(mockFrequencyDates);

    vi.mocked(createFrequencySettlementJob).mockResolvedValue(11);

    await handler(mockRequest, mockReply);

    expect(createFrequencySettlementJob).toHaveBeenCalledWith(
      {
        fromDate: new Date(mockRequest.body.fromDate),
        toDate: new Date(mockRequest.body.toDate),
      },
      mockFrequency.frequencyId,
      mockRequest.userProfile.id,
      mockRequest.server.prisma
    );

    expect(mockReply.send).toHaveBeenCalledWith({
      success: true,
      data: {
        settlement: {
          jobId: 11,
          frequencyName: "Semi-Monthly",
          fromDate: mockRequest.body.fromDate,
          toDate: mockRequest.body.toDate,
        },
      },
    });
  });

  it("should create frequency settlement schedules", async () => {
    vi.mocked(getFrequency).mockResolvedValue(mockFrequency);

    vi.mocked(calculateFrequencyDates).mockResolvedValue(mockFrequencyDates);

    vi.mocked(createFrequencySettlementJob).mockResolvedValue(11);

    vi.mocked(getAllCustomersByFrequency).mockResolvedValue([
      customer1,
      customer2,
    ]);

    await handler(mockRequest, mockReply);

    expect(createFrequencySettlementSchedule).toHaveBeenCalledWith(
      customer1,
      mockFrequencyDates.fromDate,
      mockFrequencyDates.toDate,
      mockRequest.server.prisma
    );
    expect(createFrequencySettlementSchedule).toHaveBeenCalledWith(
      customer2,
      mockFrequencyDates.fromDate,
      mockFrequencyDates.toDate,
      mockRequest.server.prisma
    );
  });

  it("should calculate settlements and save them", async () => {
    vi.mocked(getFrequency).mockResolvedValue(mockFrequency);

    vi.mocked(calculateFrequencyDates).mockResolvedValue(mockFrequencyDates);

    vi.mocked(createFrequencySettlementJob).mockResolvedValue(11);

    vi.mocked(getAllCustomersByFrequency).mockResolvedValue([
      customer1,
      customer2,
    ]);

    vi.mocked(createFrequencySettlementSchedule)
      .mockResolvedValueOnce(mockSchedule1)
      .mockResolvedValueOnce(mockSchedule2);

    vi.mocked(calculateSettlements).mockResolvedValue(mockSettlementsResult);

    await handler(mockRequest, mockReply);

    const expectedCalculationOptions = {
      includeKyc: true,
      rateDeterminingInterval: {
        fromDate: startOfPreviousMonthOnly(mockFrequencyDates.fromDate),
        toDate: endOfPreviousMonthOnly(mockFrequencyDates.toDate),
      },
    };

    expect(calculateSettlements).toHaveBeenCalledWith(
      [customer1, customer2],
      {
        fromDate: mockFrequencyDates.fromDate,
        toDate: mockFrequencyDates.toDate,
      },
      mockRequest.server.prisma,
      expectedCalculationOptions
    );

    expect(saveFrequencySettlements).toHaveBeenCalledWith(
      mockSettlementsResult,
      mockFrequencyDates.fromDate,
      mockFrequencyDates.toDate,
      mockRequest.server.prisma
    );
  });

  it("should update frequency settlement job to success", async () => {
    vi.mocked(getFrequency).mockResolvedValue(mockFrequency);

    vi.mocked(calculateFrequencyDates).mockResolvedValue(mockFrequencyDates);

    vi.mocked(createFrequencySettlementJob).mockResolvedValue(11);

    vi.mocked(getAllCustomersByFrequency).mockResolvedValue([
      customer1,
      customer2,
    ]);

    vi.mocked(createFrequencySettlementSchedule)
      .mockResolvedValueOnce(mockSchedule1)
      .mockResolvedValueOnce(mockSchedule2);

    vi.mocked(calculateSettlements).mockResolvedValue(mockSettlementsResult);

    await handler(mockRequest, mockReply);

    expect(updateFrequencySettlementJob).toHaveBeenCalledWith(
      11,
      expect.any(Object),
      "SUCCESS",
      mockRequest.server.prisma
    );
  });

  it("should update frequency settlement job to error when error occurs during settlement calculation", async () => {
    vi.mocked(getFrequency).mockResolvedValue(mockFrequency);

    vi.mocked(calculateFrequencyDates).mockResolvedValue(mockFrequencyDates);

    vi.mocked(createFrequencySettlementJob).mockResolvedValue(11);

    vi.mocked(getAllCustomersByFrequency).mockResolvedValue([
      customer1,
      customer2,
    ]);

    vi.mocked(createFrequencySettlementSchedule)
      .mockResolvedValueOnce(mockSchedule1)
      .mockResolvedValueOnce(mockSchedule2);

    vi.mocked(calculateSettlements).mockRejectedValue(
      new Error("Error calculating settlements!")
    );

    await handler(mockRequest, mockReply);

    expect(updateFrequencySettlementJob).toHaveBeenCalledWith(
      11,
      expect.any(Object),
      "ERROR",
      mockRequest.server.prisma
    );
  });
});
