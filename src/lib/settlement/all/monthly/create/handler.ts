import { getAllCustomers } from "@lib/customer/repository/get-customer";

import { type CalculationOptions } from "../../../../../types/settlement";
import {
  startOfTheMonthOnly,
  endOfTheMonthOnly,
} from "../../../../../utils/date-only";
import { calculateSettlements } from "../../../functions/calculate-settlements";
import {
  getMonthlySettlementJobInProgress,
  getMonthlySettlementJob,
  createMonthlySettlementJob,
  updateMonthlySettlementJob,
} from "../../../repository/settlement-job";
import { saveMonthlySettlements } from "../../../repository/settlement-monthly";

import type { FastifyReply, FastifyRequest } from "fastify";

type RequestBody = {
  month: number;
  year: number;
};

export const handler = async (
  request: FastifyRequest<{ Body: RequestBody }>,
  reply: FastifyReply
) => {
  const { month, year } = request.body;

  const date = { month, year, day: 1 };
  const startDate = startOfTheMonthOnly(date);
  const endDate = endOfTheMonthOnly(date);
  const currentDate = new Date();

  if (
    startDate.year > currentDate.getFullYear() ||
    (startDate.year === currentDate.getFullYear() &&
      startDate.month >= currentDate.getMonth() + 1)
  ) {
    return reply.status(403).send({
      message: "The provided date must be earlier than the current month.",
    });
  }

  const progressJob = await getMonthlySettlementJobInProgress(
    request.server.prisma
  );

  if (progressJob) {
    return reply.status(403).send({
      message: "Monthly settlement generation is currently in progress.",
    });
  }

  const existingJob = await getMonthlySettlementJob(
    month,
    year,
    request.server.prisma
  );

  if (existingJob && existingJob.status === "SUCCESS") {
    return reply.status(403).send({
      message: "Monthly settlement already generated",
    });
  }

  const start = performance.now();

  const jobId = await createMonthlySettlementJob(
    month,
    year,
    request.userProfile.id,
    request.server.prisma
  );

  await reply.send({
    jobId,
    message: "Monthly settlement calculation started",
  });

  let totalCount = 0;
  let successCount = 0;
  let errorCount = 0;
  let skippedCount = 0;

  // We only calculate Merchant settlements for now. Non-Merchant settlements
  // will be calculated in the future
  try {
    const customers = await getAllCustomers(request.server.prisma);
    totalCount = customers.length;
    const batchSize = 20;
    // Split the customers array into chunks of batchSize for memory management
    const customerChunks = chunkArray(customers, batchSize);

    const options: CalculationOptions = {
      trueUp: {
        rateDeterminingInterval: {
          fromDate: startDate,
          toDate: endDate,
        },
      },
    };

    for (const chunk of customerChunks) {
      // eslint-disable-next-line no-await-in-loop
      const settlements = await calculateSettlements(
        chunk,
        {
          fromDate: startDate,
          toDate: endDate,
        },
        request.server.prisma,
        options
      );

      // eslint-disable-next-line no-await-in-loop
      await saveMonthlySettlements(
        jobId,
        { month, year },
        settlements,
        request.server.prisma
      );

      for (const settlement of Object.values(settlements)) {
        if (settlement.status === "ERROR") {
          errorCount += 1;
        }

        if (settlement.status === "SUCCESS") {
          successCount += 1;
        }

        if (settlement.status === "SKIPPED") {
          skippedCount += 1;
        }
      }
    }

    const meta = {
      totalCount,
      successCount,
      errorCount,
      skippedCount,
      timeTakenInSeconds: Number.parseFloat(
        ((performance.now() - start) / 1000).toFixed(2)
      ),
    };
    await updateMonthlySettlementJob(
      jobId,
      meta,
      "SUCCESS",
      request.server.prisma
    );
  } catch (error) {
    request.log.error(
      error,
      `Error calculating monthly settlements for month ${month} year ${year}`
    );
    const meta = {
      errorMessage: error instanceof Error ? error.message : "Unknown error",
      timeTakenInSeconds: (performance.now() - start) / 1000,
    };
    await updateMonthlySettlementJob(
      jobId,
      meta,
      "ERROR",
      request.server.prisma
    );
  }
};

function chunkArray<T>(array: T[], size: number): T[][] {
  const chunkedArray: T[][] = [];

  for (let index = 0; index < array.length; index += size) {
    chunkedArray.push(array.slice(index, index + size));
  }

  return chunkedArray;
}
