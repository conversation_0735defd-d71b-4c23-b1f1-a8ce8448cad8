import { deleteMonthlySettlementJobs } from "../../../repository/settlement-job";

import type { FastifyReply, FastifyRequest } from "fastify";

type RequestBody = {
  jobIds: number[];
};

export const handler = async (
  request: FastifyRequest<{ Body: RequestBody }>,
  reply: FastifyReply
) => {
  try {
    const { jobIds } = request.body;

    await deleteMonthlySettlementJobs(jobIds, request.server.prisma);

    return await reply.send({
      success: true,
      message: "Monthly settlement jobs deleted successfully",
    });
  } catch (error) {
    request.log.error(error);

    return reply.code(500).send({
      success: false,
      message: "Error deleting monthly history jobs, please try again.",
      error: { message: (error as Error).message },
    });
  }
};
