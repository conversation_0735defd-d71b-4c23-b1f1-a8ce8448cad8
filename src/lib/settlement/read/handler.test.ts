import { type FastifyReply, type FastifyRequest } from "fastify";

import * as GetSettlements from "./get-settlements";
import { handler } from "./handler";
import * as GetFilterConditions from "./helpers/filters/get-filter-conditions";

describe("read settlements handler", () => {
  beforeEach(() => {
    const spyGetFilterConditions = vi.spyOn(
      GetFilterConditions,
      "getFilterConditions"
    );
    spyGetFilterConditions.mockReturnValue({
      joinConditions: [],
      havingConditions: [],
      whereConditions: [],
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should return settlements", async () => {
    const spyGetSettlements = vi
      .spyOn(GetSettlements, "getSettlements")
      .mockResolvedValue({
        totalCount: 1,
        settlements: [
          {
            customerId: 1,
            fromDate: new Date("2025-01-01"),
            toDate: new Date("2025-01-02"),
            summary: {
              id: "",
              customerName: "",
              serviceNumber: "",
              customerType: "",
              status: "Skipped",
              netPayout: "0",
              endBalance: "0",
              fromDate: ``,
              toDate: ``,
              platformSettlements: {},
            },
          },
        ],
      });

    const mockRequest = {
      body: {
        offset: 0,
        limit: 10,
        sort: "From Date: Old - New",
      },
      server: {
        prisma: {
          $transaction: vi.fn(),
        },
      },
    } as unknown as FastifyRequest;

    const mockReply = {
      send: vi.fn(),
      code: vi.fn().mockReturnThis(),
    } as unknown as FastifyReply;

    await handler(mockRequest, mockReply);

    expect(spyGetSettlements).toHaveBeenCalled();
    expect(mockReply.send).toHaveBeenCalledWith({
      totalCount: 1,
      settlements: [
        {
          customerId: 1,
          fromDate: new Date("2025-01-01"),
          toDate: new Date("2025-01-02"),
          summary: {
            id: "",
            customerName: "",
            serviceNumber: "",
            customerType: "",
            status: "Skipped",
            netPayout: "0",
            endBalance: "0",
            fromDate: ``,
            toDate: ``,
            platformSettlements: {},
          },
        },
      ],
    });
  });

  it("should handle errors", async () => {
    const errorMessage = "Some kind of error";
    const spyGetSettlements = vi
      .spyOn(GetSettlements, "getSettlements")
      .mockRejectedValue(new Error(errorMessage));

    const mockRequest = {
      body: {
        offset: 0,
        limit: 10,
        sort: "From Date: Old - New",
      },
      server: {
        prisma: {
          $transaction: vi.fn(),
        },
      },
      log: {
        error: vi.fn(),
      },
    } as unknown as FastifyRequest;

    const mockReply = {
      send: vi.fn(),
      code: vi.fn().mockReturnThis(),
    } as unknown as FastifyReply;

    await handler(mockRequest, mockReply);

    expect(spyGetSettlements).toHaveBeenCalled();
    expect(mockReply.code).toHaveBeenCalledWith(500);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: errorMessage,
    });
    expect(mockRequest.log.error).toHaveBeenCalledWith(new Error(errorMessage));
  });
});
