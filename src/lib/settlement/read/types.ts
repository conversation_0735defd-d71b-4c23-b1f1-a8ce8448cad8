import { type SettlementSummary } from "../repository/types";

export type CustomerSettlementsGroup = {
  customerId: number;
  fromDate: Date;
  toDate: Date;
};

export type GroupedSettlements = {
  customerId: number;
  fromDate: Date;
  toDate: Date;
  summary: SettlementSummary;
};

export type SettlementFilters = {
  nameOrServiceNumber: string | undefined;
  clientType: string | undefined;
  displayAdjusted: string | undefined;
  state: string | undefined;
  status: string | undefined;
  frequency: string | undefined;
  startDate: string | undefined;
  endDate: string | undefined;
  sortKey: string | undefined;
  sortOrder: string | undefined;
  offset: number | undefined;
  limit: number | undefined;
};
