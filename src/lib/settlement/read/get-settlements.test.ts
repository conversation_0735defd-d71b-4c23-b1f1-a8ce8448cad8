import { getSettlements } from "./get-settlements";
import * as GetGroupedCustomerSettlementsBySort from "./helpers/get-grouped-customer-settlements-by-sort";
import * as GetGroupedSettlementsTotalCount from "./helpers/get-grouped-settlements-count";
import * as GroupSettlementData from "./helpers/group-settlement-data";
import * as GetAuthToken from "../../../services/blusky/authentication";
import * as GetEndBalance from "../../../services/blusky/end-balance";

describe("getSettlements", () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should return settlements and total count", async () => {
    const mockPrismaFunctions = {
      customerSettlements: {
        findMany: vi.fn(),
      },
    };

    const mockPrisma = {
      $transaction: vi
        .fn()
        // eslint-disable-next-line max-len
        // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call
        .mockImplementation((callback) => callback(mockPrismaFunctions)),
    };

    const offset = 0;
    const limit = 10;
    const sortKey = "fromDate";
    const sortOrder = "asc";

    const spyGetGroupedCustomerSettlementsBySort = vi
      .spyOn(
        GetGroupedCustomerSettlementsBySort,
        "getGroupedCustomerSettlementsBySort"
      )
      .mockResolvedValue([
        {
          customerId: 1,
          fromDate: new Date("2025-01-01"),
          toDate: new Date("2025-01-02"),
        },
      ]);

    const spyGroupSettlementData = vi
      .spyOn(GroupSettlementData, "groupSettlementData")
      .mockImplementation(async () => {
        return {
          customerId: 1,
          fromDate: new Date("2025-01-01"),
          toDate: new Date("2025-01-02"),
          summary: {
            id: "123",
            customerName: "test",
            serviceNumber: "12345",
            customerType: "Merchant",
            status: "Skipped",
            netPayout: "0",
            endBalance: "111.11",
            fromDate: "2025-01-01",
            toDate: "2025-01-02",
            platformSettlements: {
              // eslint-disable-next-line @typescript-eslint/naming-convention
              SUMMARY: {
                isAdjusted: false,
              },
              // eslint-disable-next-line @typescript-eslint/naming-convention
              RTO: {
                isAdjusted: true,
              },
              // eslint-disable-next-line @typescript-eslint/naming-convention
              ETI: {
                isAdjusted: false,
              },
              // eslint-disable-next-line @typescript-eslint/naming-convention
              ACH: {
                isAdjusted: true,
              },
            },
          },
        };
      });

    const spyGetGroupedSettlementsTotalCount = vi
      .spyOn(GetGroupedSettlementsTotalCount, "getGroupedSettlementsTotalCount")
      .mockResolvedValue(1);

    const spyGetAuthToken = vi
      .spyOn(GetAuthToken, "getAuthToken")
      .mockResolvedValue("abc");
    const spyGetEndBalance = vi
      .spyOn(GetEndBalance, "getEndBalance")
      .mockResolvedValue({
        // eslint-disable-next-line @typescript-eslint/naming-convention
        12_345: {
          customerName: "test",
          endBalance: 111.11,
          currentBalance: 222.22,
          totalBalance: 333.33,
        },
      });

    const promiseAllSpy = vi.spyOn(Promise, "all");

    const result = await getSettlements({
      // @ts-expect-error mocking Prisma client
      prisma: mockPrisma,
      joinConditions: [],
      havingConditions: [],
      whereConditions: [],
      offset,
      limit,
      sortKey,
      sortOrder,
    });

    expect(spyGetGroupedCustomerSettlementsBySort).toHaveBeenCalledWith({
      tx: mockPrismaFunctions,
      joinConditions: [],
      havingConditions: [],
      whereConditions: [],
      offset,
      limit,
      sortKey,
      sortOrder,
    });

    expect(spyGetGroupedSettlementsTotalCount).toHaveBeenCalledWith(
      mockPrismaFunctions,
      [],
      [],
      []
    );

    expect(spyGroupSettlementData).toHaveBeenCalledTimes(1);

    expect(promiseAllSpy).toHaveBeenCalled();

    expect(spyGetAuthToken).toHaveBeenCalledWith();
    expect(spyGetEndBalance).toHaveBeenCalledWith(["12345"], "abc");

    expect(result).toEqual({
      totalCount: 1,
      settlements: [
        {
          customerId: 1,
          fromDate: new Date("2025-01-01"),
          toDate: new Date("2025-01-02"),
          summary: {
            id: "123",
            customerName: "test",
            serviceNumber: "12345",
            customerType: "Merchant",
            status: "Skipped",
            netPayout: "0",
            endBalance: "111.11",
            fromDate: "2025-01-01",
            toDate: "2025-01-02",
            platformSettlements: {
              // eslint-disable-next-line @typescript-eslint/naming-convention
              SUMMARY: {
                isAdjusted: false,
              },
              // eslint-disable-next-line @typescript-eslint/naming-convention
              RTO: {
                isAdjusted: true,
              },
              // eslint-disable-next-line @typescript-eslint/naming-convention
              ETI: {
                isAdjusted: false,
              },
              // eslint-disable-next-line @typescript-eslint/naming-convention
              ACH: {
                isAdjusted: true,
              },
            },
          },
        },
      ],
    });
  });
});
