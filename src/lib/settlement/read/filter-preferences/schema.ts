/* eslint-disable @typescript-eslint/naming-convention */
import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Fetch or Update Settlement Preference of User",
  description:
    "Fetches or saves the settlement filter preference of the user, or default preferences if not set.",
  tags: [tags.settlement],
  body: {
    type: "object",
    properties: {
      nameOrServiceNumber: { type: "string" },
      clientType: { type: "string" },
      displayAdjusted: { type: "string" },
      state: { type: "string" },
      status: { type: "string" },
      frequency: { type: "string" },
      startDate: { type: "string" },
      endDate: { type: "string" },
      sortKey: { type: "string" },
      sortOrder: { type: "string" },
      offset: { type: "number" },
      limit: { type: "number" },
    },
    required: [
      "nameOrServiceNumber",
      "clientType",
      "displayAdjusted",
      "state",
      "status",
      "frequency",
      "startDate",
      "endDate",
      "sortKey",
      "sortOrder",
      "offset",
      "limit",
    ],
    additionalProperties: true,
  },
  response: {
    200: {
      description: "Successful response",
      type: "object",
      properties: {
        data: {
          type: "object",
          properties: {
            filters: { type: "object" },
            pageNumber: { type: "number" },
            recordsPerPage: { type: "number" },
          },
          required: ["filters", "pageNumber", "recordsPerPage"],
        },
        success: { type: "boolean" },
        message: { type: "string" },
      },
    },
    500: {
      description: "Error response",
      type: "object",
      properties: {
        message: { type: "string" },
      },
      required: ["message"],
    },
  },
};
