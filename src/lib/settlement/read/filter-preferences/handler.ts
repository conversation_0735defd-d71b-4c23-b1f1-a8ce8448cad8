import type { FastifyReply, FastifyRequest } from "fastify";
import { getSettlementPreference } from "./repository";

export const handler = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const { prisma } = request.server;
    const userId = request.userProfile.id;

    console.log(11111111, userId);

    // if (request.method === "POST") {
    //   await saveSettlementPreference(userId, request.body, prisma);

    //   return await reply.send({ success: true, message: "Preference saved." });
    // }

    const preference = await getSettlementPreference(userId, prisma);

    console.log(222222, preference);

    return await reply.send(`{ data: preference }`);
  } catch (error) {
    request.log.error(error);
    await reply.code(500).send({
      message: (error as Error).message,
    });
  }
};
