import { type SettlementFilters } from "../types";

import type { PrismaClient } from "@prisma/client";

export const getSettlementPreference = async (
  userId: number,
  prisma: PrismaClient
) => {
  const userPreference = await prisma.userPreference.findUnique({
    where: { userId },
    select: {
      settlementPreference: true,
    },
  });

  return userPreference?.settlementPreference ?? null;
};

export const saveSettlementPreference = async (
  userId: number,
  settlementFilters: SettlementFilters,
  prisma: PrismaClient
) => {
  await prisma.userPreference.upsert({
    where: { userId },
    update: {
      settlementPreference: settlementFilters,
    },
    create: {
      userId,
      settlementPreference: settlementFilters,
    },
  });
};
