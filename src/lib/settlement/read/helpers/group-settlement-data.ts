import { type PlatformCode } from "@lib/settlement/repository/types";
import { type Prisma } from "@prisma/client";
import { format } from "date-fns";

import { getSettlementStateAndMessage } from "./get-settlement-state-and-message";
import { settlementValuesAreNotNullOrZeroOrUndefined } from "./settlement-values-are-not-null-or-zero-or-undefined";
import { type GroupedSettlements } from "../types";

export async function groupSettlementData(
  tx: Prisma.TransactionClient,
  groupData: GroupedSettlements
): Promise<GroupedSettlements> {
  const settlements = await tx.customerSettlements.findMany({
    where: {
      customerId: groupData.customerId,
      fromDate: groupData.fromDate,
      toDate: groupData.toDate,
    },
    select: {
      fromDate: true,
      toDate: true,
      customerSettlementsId: true,
      customerId: true,
      transactionCount: true,
      totalTransactionAmount: true,
      refundCount: true,
      totalRefundAmount: true,
      gatewayFee: true,
      transactionFee: true,
      salesFee: true,
      refundFee: true,
      totalFailedAmount: true,
      total2FaRejectAmount: true,
      total2FaRejectCount: true,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      txnAmountRTO_R: true,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      txnCountETI_R1: true,
      minimumFeeTotal: true,
      minimumFeeCount: true,
      totalMinimumAmount: true,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      partialReturnAmountRTO: true,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      partialReturnCountRTO: true,
      platform: {
        select: {
          platformCode: true,
        },
      },
      customerSettlementAdjustments: {
        select: {
          amount: true,
        },
      },
      customer: {
        select: {
          customerName: true,
          serviceNumber: true,
          enabled: true,
        },
      },
      customerCustomerType: {
        select: {
          customerCustomerTypeId: true,
          customerType: {
            select: {
              customerTypeName: true,
            },
          },
        },
      },
      // eslint-enable @typescript-eslint/naming-convention
    },
  });

  const promises = settlements.map(async (settlement) => {
    if (groupData.summary.platformSettlements === undefined) {
      groupData.summary.platformSettlements = {};
    }

    const platformIsNotZero = settlementValuesAreNotNullOrZeroOrUndefined(
      settlement,
      [
        "transactionCount",
        "totalTransactionAmount",
        "refundCount",
        "totalRefundAmount",
        "gatewayFee",
        "transactionFee",
        "salesFee",
        "refundFee",
        "totalFailedAmount",
        "total2FaRejectAmount",
        "total2FaRejectCount",
        "txnAmountRTO_R",
        "txnCountETI_R1",
        "minimumFeeTotal",
        "minimumFeeCount",
        "totalMinimumAmount",
        "partialReturnAmountRTO",
        "partialReturnCountRTO",
      ]
    );

    let adjustmentFound = -1;

    // If the settlement has adjustments
    if (settlement.customerSettlementAdjustments.length > 0) {
      adjustmentFound = settlement.customerSettlementAdjustments.findIndex(
        (adjustment) => adjustment?.amount?.toNumber() !== 0
      );
    }

    if (adjustmentFound !== -1 || platformIsNotZero) {
      groupData.summary.platformSettlements[
        settlement.platform.platformCode as PlatformCode
      ] = { isAdjusted: adjustmentFound !== -1 };
    }

    if (settlement.platform.platformCode === "SUMMARY") {
      const { fromDate, toDate } = settlement;
      const { state, message } = await getSettlementStateAndMessage(
        {
          customerCustomerTypeId:
            settlement.customerCustomerType?.customerCustomerTypeId,
          fromDate,
          toDate,
        },
        tx
      );

      // Note we use the summary settlement as the unique id of the grouped settlements
      groupData.summary.id = String(settlement.customerSettlementsId);
      groupData.summary.customerName = settlement.customer.customerName;
      groupData.summary.serviceNumber = settlement.customer.serviceNumber;
      groupData.summary.customerType =
        settlement.customerCustomerType?.customerType.customerTypeName ??
        "Unknown";
      groupData.summary.status = state;
      groupData.summary.netPayout = "0";
      groupData.summary.fromDate = format(fromDate, "yyyy-MM-dd");
      groupData.summary.toDate = format(toDate, "yyyy-MM-dd");

      if (message) {
        groupData.summary.error = message;
      }
    }
  });

  await Promise.all(promises);

  return groupData;
}
