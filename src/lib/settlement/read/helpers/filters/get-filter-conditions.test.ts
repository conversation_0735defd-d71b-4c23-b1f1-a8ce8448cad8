import { Prisma } from "@prisma/client";

import { stateStatusJoin } from "./constants";
import { getFilterConditions } from "./get-filter-conditions";

describe("getFilterConditions", () => {
  it("should return correct filter conditions for nameOrServiceNumber", () => {
    const filters = {
      nameOrServiceNumber: "test",
      clientType: undefined,
      displayAdjusted: undefined,
      state: undefined,
      frequency: undefined,
      startDate: undefined,
      endDate: undefined,
      status: "No Filter",
    };

    const result = getFilterConditions(filters);
    expect(result.whereConditions).toEqual([
      Prisma.sql`(c.customerName LIKE ${"%test%"} OR c.serviceNumber LIKE ${"%test%"})`,
    ]);
  });

  it("should return correct filter conditions for clientType", () => {
    const filters = {
      nameOrServiceNumber: undefined,
      clientType: "testClient",
      displayAdjusted: undefined,
      state: undefined,
      frequency: undefined,
      startDate: undefined,
      endDate: undefined,
      status: "No Filter",
    };

    const result = getFilterConditions(filters);
    expect(result.whereConditions).toEqual([
      Prisma.sql`(ct.customerTypeName = ${"testClient"})`,
    ]);
  });

  it("should return correct filter conditions for displayAdjusted Adjustments Only", () => {
    const filters = {
      nameOrServiceNumber: undefined,
      clientType: undefined,
      displayAdjusted: "Adjustments Only",
      state: undefined,
      frequency: undefined,
      startDate: undefined,
      endDate: undefined,
      status: "No Filter",
    };

    const result = getFilterConditions(filters);
    expect(result.whereConditions).toEqual([
      Prisma.sql`(EXISTS (
    SELECT 1
    FROM customerSettlementAdjustments csa
    WHERE s.customerSettlementsId = csa.customerSettlementsId
      AND csa.amount > 0
))`,
    ]);
  });

  it("should return correct filter conditions for displayAdjusted No Adjustments", () => {
    const filters = {
      nameOrServiceNumber: undefined,
      clientType: undefined,
      displayAdjusted: "No Adjustments",
      state: undefined,
      frequency: undefined,
      startDate: undefined,
      endDate: undefined,
      status: "No Filter",
    };

    const result = getFilterConditions(filters);
    expect(result.whereConditions).toEqual([]);
    expect(result.joinConditions).toEqual([
      Prisma.sql`LEFT JOIN customerSettlementAdjustments csa ON s.customerSettlementsId = csa.customerSettlementsId`,
    ]);
    expect(result.havingConditions).toEqual([
      Prisma.sql`SUM(CASE WHEN csa.amount > 0 THEN 1 ELSE 0 END) = 0`,
    ]);
  });

  it("should return correct filter conditions for state", () => {
    const filters = {
      nameOrServiceNumber: undefined,
      clientType: undefined,
      displayAdjusted: undefined,
      state: "testState",
      frequency: undefined,
      startDate: undefined,
      endDate: undefined,
      status: "No Filter",
    };

    const result = getFilterConditions(filters);
    expect(result.whereConditions).toEqual([
      Prisma.sql`(ss.stateName = ${"testState"})`,
    ]);
    expect(result.joinConditions).toEqual([stateStatusJoin]);
  });

  it("should return correct filter conditions for status Settlement Approval", () => {
    const filters = {
      nameOrServiceNumber: undefined,
      clientType: undefined,
      displayAdjusted: undefined,
      state: undefined,
      frequency: undefined,
      startDate: undefined,
      endDate: undefined,
      status: "Settlement Approval",
    };

    const result = getFilterConditions(filters);
    expect(result.whereConditions).toEqual([
      Prisma.sql`(ss.stateName = 'Approval Pending' OR ss.stateName = 'Approval Processing' OR\n      ss.stateName = 'Approval Success' OR ss.stateName = 'Approval Error')`,
    ]);
    expect(result.joinConditions).toEqual([stateStatusJoin]);
  });

  it("should return correct filter conditions for status Settlement Generation", () => {
    const filters = {
      nameOrServiceNumber: undefined,
      clientType: undefined,
      displayAdjusted: undefined,
      state: undefined,
      frequency: undefined,
      startDate: undefined,
      endDate: undefined,
      status: "Settlement Generation",
    };

    const result = getFilterConditions(filters);
    expect(result.whereConditions).toEqual([
      Prisma.sql`(ss.stateName = 'Skipped' OR ss.stateName = 'Error' OR ss.stateName = 'Processing')`,
    ]);
    expect(result.joinConditions).toEqual([stateStatusJoin]);
  });

  it("should return correct filter conditions for frequency", () => {
    const filters = {
      nameOrServiceNumber: undefined,
      clientType: undefined,
      displayAdjusted: undefined,
      state: undefined,
      frequency: "testFrequency",
      startDate: undefined,
      endDate: undefined,
      status: "No Filter",
    };

    const result = getFilterConditions(filters);
    expect(result.whereConditions).toEqual([
      Prisma.sql`(sf.statementFrequencyName = ${"testFrequency"})`,
    ]);
    expect(result.joinConditions).toEqual([
      Prisma.sql`JOIN statementFrequency sf ON cct.statementFrequencyId = sf.statementFrequencyId`,
    ]);
  });

  it("should return correct filter conditions for startDate and endDate", () => {
    const filters = {
      nameOrServiceNumber: undefined,
      clientType: undefined,
      displayAdjusted: undefined,
      state: undefined,
      frequency: undefined,
      startDate: "2023-01-01",
      endDate: "2023-01-31",
      status: "No Filter",
    };

    const result = getFilterConditions(filters);
    expect(result.whereConditions).toEqual([
      Prisma.sql`(s.fromDate >= ${"2023-01-01"} AND s.toDate <= ${"2023-01-31"})`,
    ]);
  });

  it("should return correct filter conditions for all filters adjustments only", () => {
    const filters = {
      nameOrServiceNumber: "test",
      clientType: "testClient",
      displayAdjusted: "Adjustments Only",
      state: "testState",
      frequency: "testFrequency",
      startDate: "2023-01-01",
      endDate: "2023-01-31",
      status: "No Filter",
    };

    const result = getFilterConditions(filters);
    expect(result.whereConditions).toEqual([
      Prisma.sql`(c.customerName LIKE ${"%test%"} OR c.serviceNumber LIKE ${"%test%"})`,
      Prisma.sql`(ct.customerTypeName = ${"testClient"})`,
      Prisma.sql`(EXISTS (
    SELECT 1
    FROM customerSettlementAdjustments csa
    WHERE s.customerSettlementsId = csa.customerSettlementsId
      AND csa.amount > 0
))`,
      Prisma.sql`(ss.stateName = ${"testState"})`,
      Prisma.sql`(sf.statementFrequencyName = ${"testFrequency"})`,
      Prisma.sql`(s.fromDate >= ${"2023-01-01"} AND s.toDate <= ${"2023-01-31"})`,
    ]);
    expect(result.joinConditions).toEqual([
      stateStatusJoin,
      Prisma.sql`JOIN statementFrequency sf ON cct.statementFrequencyId = sf.statementFrequencyId`,
    ]);

    expect(result.havingConditions).toEqual([]);
  });

  it("should return correct filter conditions for all filters no adjustments", () => {
    const filters = {
      nameOrServiceNumber: "test",
      clientType: "testClient",
      displayAdjusted: "No Adjustments",
      state: "testState",
      frequency: "testFrequency",
      startDate: "2023-01-01",
      endDate: "2023-01-31",
      status: "No Filter",
    };

    const result = getFilterConditions(filters);
    expect(result.whereConditions).toEqual([
      Prisma.sql`(c.customerName LIKE ${"%test%"} OR c.serviceNumber LIKE ${"%test%"})`,
      Prisma.sql`(ct.customerTypeName = ${"testClient"})`,
      Prisma.sql`(ss.stateName = ${"testState"})`,
      Prisma.sql`(sf.statementFrequencyName = ${"testFrequency"})`,
      Prisma.sql`(s.fromDate >= ${"2023-01-01"} AND s.toDate <= ${"2023-01-31"})`,
    ]);
    expect(result.joinConditions).toEqual([
      Prisma.sql`LEFT JOIN customerSettlementAdjustments csa ON s.customerSettlementsId = csa.customerSettlementsId`,
      stateStatusJoin,
      Prisma.sql`JOIN statementFrequency sf ON cct.statementFrequencyId = sf.statementFrequencyId`,
    ]);

    expect(result.havingConditions).toEqual([
      Prisma.sql`SUM(CASE WHEN csa.amount > 0 THEN 1 ELSE 0 END) = 0`,
    ]);
  });

  it("should return empty raw query conditions when no filters are provided", () => {
    const filters = {
      nameOrServiceNumber: undefined,
      clientType: undefined,
      displayAdjusted: undefined,
      state: undefined,
      frequency: undefined,
      startDate: undefined,
      endDate: undefined,
      status: "No Filter",
    };

    const result = getFilterConditions(filters);
    expect(result.whereConditions).toEqual([]);
    expect(result.joinConditions).toEqual([]);
    expect(result.havingConditions).toEqual([]);
  });
});
