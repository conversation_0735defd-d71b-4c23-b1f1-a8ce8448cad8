import { Prisma } from "@prisma/client";

import { getSubQueryForSettlementsWithFilters } from "./filters/get-sub-query-for-settlements-with-filters";
import { type CustomerSettlementsGroup } from "../types";

const sortByValues: string[] = ["fromDate", "toDate", "clientName"];

async function getGroupedCustomerSettlementsBySort({
  tx,
  joinConditions,
  whereConditions,
  havingConditions,
  offset,
  limit,
  sortKey,
  sortOrder,
}: {
  tx: Prisma.TransactionClient;
  joinConditions: Prisma.Sql[];
  whereConditions: Prisma.Sql[];
  havingConditions: Prisma.Sql[];
  offset: number;
  limit: number;
  sortKey: string;
  sortOrder: "asc" | "desc";
}): Promise<CustomerSettlementsGroup[]> {
  // We are doing raw queries here as we need to sort and filter by numerous factors.
  // If the DB was designed to take this into account we might have been able to use
  // prisma to filterbut due to the structure we need to rely on raw queries if we want
  // to filter all in one call
  switch (sortKey) {
    case sortByValues[0]: {
      // "From Date: Old - New or New - Old"

      const query = Prisma.sql`
          ${getSubQueryForSettlementsWithFilters(whereConditions, joinConditions, havingConditions)}
          ORDER BY s.fromDate ${sortOrder === "desc" ? Prisma.sql`DESC` : Prisma.sql`ASC`}, c.customerName ASC 
          LIMIT ${limit} OFFSET ${offset};
          `;

      return tx.$queryRaw<CustomerSettlementsGroup[]>(query);
    }

    case sortByValues[1]: {
      // "To Date: Old - New or New - Old"

      const query = Prisma.sql`
        ${getSubQueryForSettlementsWithFilters(whereConditions, joinConditions, havingConditions)}
          ORDER BY s.toDate ${sortOrder === "desc" ? Prisma.sql`DESC` : Prisma.sql`ASC`}, c.customerName ASC
        LIMIT ${limit} OFFSET ${offset};
        `;

      return tx.$queryRaw<CustomerSettlementsGroup[]>(query);
    }

    case sortByValues[2]: {
      // "Client Name: A-Z or Z-A"

      const query = Prisma.sql`
        ${getSubQueryForSettlementsWithFilters(whereConditions, joinConditions, havingConditions)}
          ORDER BY c.customerName ${sortOrder === "desc" ? Prisma.sql`DESC` : Prisma.sql`ASC`}, s.fromDate ASC
          LIMIT ${limit} OFFSET ${offset};
          `;

      return tx.$queryRaw<CustomerSettlementsGroup[]>(query);
    }

    default: {
      throw new Error("Invalid sort value provided");
    }
  }
}

export { getGroupedCustomerSettlementsBySort };
