import { Decimal } from "@prisma/client/runtime/library";

import {
  type Settlement,
  settlementValuesAreNotNullOrZeroOrUndefined,
} from "./settlement-values-are-not-null-or-zero-or-undefined";

describe("settlementValuesAreNotNullOrZeroOrUndefined", () => {
  it("should return true for non-null, non-zero, and defined values", () => {
    const nonZeroSettlement = {
      transactionCount: 1,
      totalTransactionAmount: 100,
      refundCount: 2,
      totalRefundAmount: 50,
      gatewayFee: 0.5,
      transactionFee: 0.2,
      salesFee: 0.3,
      refundFee: 0.1,
      totalFailedAmount: 0,
      total2FaRejectAmount: 0,
      total2FaRejectCount: 0,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      txnAmountRTO_R: 0,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      txnCountETI_R1: 0,
      minimumFeeTotal: 0,
      minimumFeeCount: 0,
      totalMinimumAmount: 0,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      partialReturnAmountRTO: 0,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      partialReturnCountRTO: 0,
    } as unknown as Settlement;

    const result = settlementValuesAreNotNullOrZeroOrUndefined(
      nonZeroSettlement,
      [
        "transactionCount",
        "totalTransactionAmount",
        "refundCount",
        "totalRefundAmount",
        "gatewayFee",
        "transactionFee",
        "salesFee",
        "refundFee",
        "totalFailedAmount",
        "total2FaRejectAmount",
        "total2FaRejectCount",
        "txnAmountRTO_R",
        "txnCountETI_R1",
        "minimumFeeTotal",
        "minimumFeeCount",
        "totalMinimumAmount",
        "partialReturnAmountRTO",
        "partialReturnCountRTO",
      ]
    );

    expect(result).toBe(true);
  });

  it("should return false for null, zero, or undefined values", () => {
    const zeroSettlement = {
      transactionCount: 0,
      totalTransactionAmount: null,
      refundCount: undefined,
      totalRefundAmount: 0,
      gatewayFee: 0,
      transactionFee: null,
      salesFee: undefined,
      refundFee: 0,
      totalFailedAmount: null,
      total2FaRejectAmount: 0,
      total2FaRejectCount: undefined,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      txnAmountRTO_R: new Decimal(0),
      // eslint-disable-next-line @typescript-eslint/naming-convention
      txnCountETI_R1: null,
      minimumFeeTotal: new Decimal(0),
      minimumFeeCount: undefined,
      totalMinimumAmount: 0,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      partialReturnAmountRTO: null,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      partialReturnCountRTO: 0,
    } as unknown as Settlement;

    const result = settlementValuesAreNotNullOrZeroOrUndefined(zeroSettlement, [
      "transactionCount",
      "totalTransactionAmount",
      "refundCount",
      "totalRefundAmount",
      "gatewayFee",
      "transactionFee",
      "salesFee",
      "refundFee",
      "totalFailedAmount",
      "total2FaRejectAmount",
      "total2FaRejectCount",
      "txnAmountRTO_R",
      "txnCountETI_R1",
      "minimumFeeTotal",
      "minimumFeeCount",
      "totalMinimumAmount",
      "partialReturnAmountRTO",
      "partialReturnCountRTO",
    ]);

    expect(result).toBe(false);
  });
});
