/* eslint-disable @typescript-eslint/naming-convention */

import { payInGroup, payOutGroup } from "@constants/transactions/platform";
import { type PlatformCode } from "@lib/settlement/repository/types";
import { Decimal } from "@prisma/client/runtime/library";

import { getSettlementStateAndMessage } from "./get-settlement-state-and-message";
import { groupSettlementData } from "./group-settlement-data";

import type { GroupedSettlements } from "@lib/settlement/read/types";
import type { PrismaClient } from "@prisma/client";

type TransactionClient = PrismaClient;

vi.mock("./get-settlement-state-and-message", () => ({
  getSettlementStateAndMessage: vi.fn().mockReturnValue({
    state: "Skipped",
    message: "No transactions found",
  }),
}));

describe("groupSettlementData", () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should group settlement data correctly", async () => {
    const mockPrismaFunctions = {
      customerSettlements: {
        findMany: vi.fn().mockResolvedValueOnce([
          {
            fromDate: new Date("2025-01-01"),
            toDate: new Date("2025-01-02"),
            customerSettlementsId: 123,
            customerId: 1,
            transactionCount: 123,
            totalTransactionAmount: new Decimal(100),
            refundCount: 1,
            totalRefundAmount: new Decimal(1),
            gatewayFee: new Decimal(0),
            transactionFee: new Decimal(0),
            salesFee: new Decimal(0),
            refundFee: new Decimal(0),
            totalFailedAmount: new Decimal(0),
            total2FaRejectAmount: new Decimal(0),
            total2FaRejectCount: 0,
            txnAmountRTO_R: 0,
            txnCountETI_R1: 0,
            minimumFeeTotal: new Decimal(0),
            minimumFeeCount: 0,
            totalMinimumAmount: new Decimal(0),
            partialReturnAmountRTO: 0,
            partialReturnCountRTO: 0,
            platform: {
              platformCode: "SUMMARY",
            },
            customerSettlementAdjustments: [{ amount: new Decimal(0) }],
            customer: {
              customerName: "test",
              serviceNumber: "12345",
              enabled: true,
            },
            customerCustomerType: {
              customerCustomerTypeId: 1,
              customerType: {
                customerTypeName: "Merchant",
              },
            },
          },
          {
            fromDate: new Date("2025-01-01"),
            toDate: new Date("2025-01-02"),
            customerSettlementsId: 1234,
            customerId: 1,
            transactionCount: 123,
            totalTransactionAmount: new Decimal(10),
            refundCount: 1,
            totalRefundAmount: new Decimal(1),
            gatewayFee: new Decimal(0),
            transactionFee: new Decimal(0),
            salesFee: new Decimal(0),
            refundFee: new Decimal(0),
            totalFailedAmount: new Decimal(0),
            total2FaRejectAmount: new Decimal(0),
            total2FaRejectCount: 0,
            txnAmountRTO_R: 0,
            txnCountETI_R1: 0,
            minimumFeeTotal: new Decimal(0),
            minimumFeeCount: 0,
            totalMinimumAmount: new Decimal(0),
            partialReturnAmountRTO: 0,
            partialReturnCountRTO: 0,
            platform: {
              platformCode: "RTO",
            },
            customerSettlementAdjustments: [{ amount: new Decimal(1) }],
            customer: {
              customerName: "test",
              serviceNumber: "12345",
              enabled: true,
            },
            customerCustomerType: {
              customerCustomerTypeId: 1,
              customerType: {
                customerTypeName: null,
              },
            },
          },
          {
            fromDate: new Date("2025-01-01"),
            toDate: new Date("2025-01-02"),
            customerSettlementsId: 1235,
            customerId: 1,
            transactionCount: 123,
            totalTransactionAmount: new Decimal(100),
            refundCount: 1,
            totalRefundAmount: new Decimal(1),
            gatewayFee: new Decimal(0),
            transactionFee: new Decimal(0),
            salesFee: new Decimal(0),
            refundFee: new Decimal(0),
            totalFailedAmount: new Decimal(0),
            total2FaRejectAmount: new Decimal(0),
            total2FaRejectCount: 0,
            txnAmountRTO_R: 0,
            txnCountETI_R1: 0,
            minimumFeeTotal: new Decimal(0),
            minimumFeeCount: 0,
            totalMinimumAmount: new Decimal(0),
            partialReturnAmountRTO: 0,
            partialReturnCountRTO: 0,
            platform: {
              platformCode: "ETI",
            },
            customerSettlementAdjustments: [{ amount: new Decimal(0) }],
            customer: {
              customerName: "test",
              serviceNumber: "12345",
              enabled: true,
            },
            customerCustomerType: {
              customerCustomerTypeId: 1,
              customerType: {
                customerTypeName: "Merchant",
              },
            },
          },
          {
            fromDate: new Date("2025-01-01"),
            toDate: new Date("2025-01-02"),
            customerSettlementsId: 6,
            customerId: 0,
            transactionCount: 0,
            totalTransactionAmount: new Decimal(0),
            refundCount: 0,
            totalRefundAmount: new Decimal(0),
            gatewayFee: new Decimal(0),
            transactionFee: new Decimal(0),
            salesFee: new Decimal(0),
            refundFee: new Decimal(0),
            totalFailedAmount: new Decimal(0),
            total2FaRejectAmount: new Decimal(0),
            total2FaRejectCount: 0,
            txnAmountRTO_R: 0,
            txnCountETI_R1: 0,
            minimumFeeTotal: new Decimal(0),
            minimumFeeCount: 0,
            totalMinimumAmount: new Decimal(0),
            partialReturnAmountRTO: 0,
            partialReturnCountRTO: 0,
            platform: {
              platformCode: "ETO",
            },
            customerSettlementAdjustments: [{ amount: new Decimal(0) }],
            customer: {
              customerName: "test",
              serviceNumber: "12345",
              enabled: true,
            },
            customerCustomerType: {
              customerCustomerTypeId: 1,
              customerType: {
                customerTypeName: "Merchant",
              },
            },
          },
          {
            fromDate: new Date("2025-01-01"),
            toDate: new Date("2025-01-02"),
            customerSettlementsId: 7,
            customerId: 0,
            transactionCount: 0,
            totalTransactionAmount: new Decimal(0),
            refundCount: 0,
            totalRefundAmount: new Decimal(0),
            gatewayFee: new Decimal(0),
            transactionFee: new Decimal(0),
            salesFee: new Decimal(0),
            refundFee: new Decimal(0),
            totalFailedAmount: new Decimal(0),
            total2FaRejectAmount: new Decimal(0),
            total2FaRejectCount: 0,
            txnAmountRTO_R: 0,
            txnCountETI_R1: 0,
            minimumFeeTotal: new Decimal(0),
            minimumFeeCount: 0,
            totalMinimumAmount: new Decimal(0),
            partialReturnAmountRTO: 0,
            partialReturnCountRTO: 0,
            platform: {
              platformCode: "ACH",
            },
            customerSettlementAdjustments: [{ amount: new Decimal(5) }],
            customer: {
              customerName: "test",
              serviceNumber: "12345",
              enabled: true,
            },
            customerCustomerType: {
              customerCustomerTypeId: 1,
              customerType: {
                customerTypeName: "Merchant",
              },
            },
          },
        ]),
      },
    };

    const groupData = {
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-02"),
      customerId: 1,
      summary: {
        platformSettlements: {},
        netPayout: "0",
      },
    };

    const result = await groupSettlementData(
      mockPrismaFunctions as unknown as TransactionClient,
      groupData as GroupedSettlements
    );

    expect(result).toEqual({
      customerId: 1,
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-02"),
      summary: {
        id: "123",
        customerName: "test",
        serviceNumber: "12345",
        customerType: "Merchant",
        status: "Skipped",
        netPayout: "82",
        fromDate: "2025-01-01",
        toDate: "2025-01-02",
        platformSettlements: {
          SUMMARY: { isAdjusted: false, isNonZero: true },
          RTO: { isAdjusted: true, isNonZero: true },
          ETO: { isAdjusted: false, isNonZero: false },
          ETI: { isAdjusted: false, isNonZero: true },
          ACH: { isAdjusted: true, isNonZero: false },
        },
        error: "No transactions found",
      },
    });
  });

  it("should group settlement data correctly with a merchant type unknown", async () => {
    const mockPrismaFunctions = {
      customerSettlements: {
        findMany: vi.fn().mockResolvedValueOnce([
          {
            fromDate: new Date("2025-01-01"),
            toDate: new Date("2025-01-02"),
            customerSettlementsId: 123,
            customerId: 1,
            transactionCount: 123,
            totalTransactionAmount: new Decimal(100),
            refundCount: 1,
            totalRefundAmount: new Decimal(1),
            gatewayFee: new Decimal(0),
            transactionFee: new Decimal(0),
            salesFee: new Decimal(0),
            refundFee: new Decimal(0),
            totalFailedAmount: new Decimal(0),
            total2FaRejectAmount: new Decimal(0),
            total2FaRejectCount: 0,
            txnAmountRTO_R: 0,
            txnCountETI_R1: 0,
            minimumFeeTotal: new Decimal(0),
            minimumFeeCount: 0,
            totalMinimumAmount: new Decimal(0),
            partialReturnAmountRTO: 0,
            partialReturnCountRTO: 0,
            platform: {
              platformCode: "SUMMARY",
            },
            customerSettlementAdjustments: [{ amount: new Decimal(0) }],
            customer: {
              customerName: "test",
              serviceNumber: "12345",
              enabled: true,
            },
            customerCustomerType: {
              customerCustomerTypeId: 1,
              customerType: {
                customerTypeName: null,
              },
            },
          },
        ]),
      },
    };

    const groupData = {
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-02"),
      customerId: 1,
      summary: {
        platformSettlements: {},
        netPayout: "0",
      },
    };

    const result = await groupSettlementData(
      mockPrismaFunctions as unknown as TransactionClient,
      groupData as GroupedSettlements
    );

    expect(result).toEqual({
      customerId: 1,
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-02"),
      summary: {
        id: "123",
        customerName: "test",
        serviceNumber: "12345",
        customerType: "Unknown",
        status: "Skipped",
        netPayout: "0",
        fromDate: "2025-01-01",
        toDate: "2025-01-02",
        platformSettlements: {
          SUMMARY: { isAdjusted: false, isNonZero: true },
        },
        error: "No transactions found",
      },
    });
  });

  it("should group settlement data correctly with no message", async () => {
    vi.mocked(getSettlementStateAndMessage).mockResolvedValue({
      state: "Skipped",
    });

    const mockPrismaFunctions = {
      customerSettlements: {
        findMany: vi.fn().mockResolvedValueOnce([
          {
            fromDate: new Date("2025-01-01"),
            toDate: new Date("2025-01-02"),
            customerSettlementsId: 123,
            customerId: 1,
            transactionCount: 123,
            totalTransactionAmount: new Decimal(100),
            refundCount: 1,
            totalRefundAmount: new Decimal(1),
            gatewayFee: new Decimal(0),
            transactionFee: new Decimal(0),
            salesFee: new Decimal(0),
            refundFee: new Decimal(0),
            totalFailedAmount: new Decimal(0),
            total2FaRejectAmount: new Decimal(0),
            total2FaRejectCount: 0,
            txnAmountRTO_R: 0,
            txnCountETI_R1: 0,
            minimumFeeTotal: new Decimal(0),
            minimumFeeCount: 0,
            totalMinimumAmount: new Decimal(0),
            partialReturnAmountRTO: 0,
            partialReturnCountRTO: 0,
            platform: {
              platformCode: "SUMMARY",
            },
            customerSettlementAdjustments: [{ amount: new Decimal(0) }],
            customer: {
              customerName: "test",
              serviceNumber: "12345",
              enabled: true,
            },
            customerCustomerType: {
              customerCustomerTypeId: 1,
              customerType: {
                customerTypeName: null,
              },
            },
          },
        ]),
      },
    };

    const groupData = {
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-02"),
      customerId: 1,
      summary: {
        platformSettlements: {},
        netPayout: "0",
      },
    };

    const result = await groupSettlementData(
      mockPrismaFunctions as unknown as TransactionClient,
      groupData as GroupedSettlements
    );

    expect(result).toEqual({
      customerId: 1,
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-02"),
      summary: {
        id: "123",
        customerName: "test",
        serviceNumber: "12345",
        customerType: "Unknown",
        status: "Skipped",
        netPayout: "0",
        fromDate: "2025-01-01",
        toDate: "2025-01-02",
        platformSettlements: {
          SUMMARY: { isAdjusted: false, isNonZero: true },
        },
      },
    });
  });
});

describe("groupSettlementData payout calculations", () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should return the initial summary unchanged if no settlements exist", async () => {
    const mockPrismaFunctions = {
      customerSettlements: {
        findMany: vi.fn().mockResolvedValueOnce([]),
      },
    };

    const groupData = {
      customerId: 1,
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-02"),
      summary: {
        platformSettlements: {},
        netPayout: "0",
      },
    };

    const result = await groupSettlementData(
      mockPrismaFunctions as unknown as TransactionClient,
      groupData as GroupedSettlements
    );

    expect(result).toEqual({
      customerId: 1,
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-02"),
      summary: {
        platformSettlements: {},
        netPayout: "0",
      },
    });
  });

  it("should calculate netPayout correctly for payIn platforms with adjustments", async () => {
    const platformCode = payInGroup[0] as PlatformCode;
    const mockPrismaFunctions = {
      customerSettlements: {
        findMany: vi.fn().mockResolvedValueOnce([
          {
            fromDate: new Date("2025-01-01"),
            toDate: new Date("2025-01-02"),
            customerSettlementsId: 100,
            customerId: 1,
            transactionCount: 10,
            totalTransactionAmount: new Decimal(1000),
            refundCount: 2,
            totalRefundAmount: new Decimal(50),
            gatewayFee: new Decimal(10),
            transactionFee: new Decimal(20),
            salesFee: new Decimal(30),
            refundFee: new Decimal(5),
            totalFailedAmount: new Decimal(0),
            total2FaRejectAmount: new Decimal(0),
            total2FaRejectCount: 0,
            txnAmountRTO_R: 0,
            txnCountETI_R1: 0,
            minimumFeeTotal: new Decimal(15),
            minimumFeeCount: 0,
            totalMinimumAmount: new Decimal(0),
            partialReturnAmountRTO: 0,
            partialReturnCountRTO: 0,
            platform: { platformCode },
            customerSettlementAdjustments: [{ amount: new Decimal(10) }],
            customer: {
              customerName: "Merchant A",
              serviceNumber: "001",
              enabled: true,
            },
            customerCustomerType: {
              customerCustomerTypeId: 1,
              customerType: { customerTypeName: "Merchant" },
            },
          },
        ]),
      },
    };

    const groupData = {
      customerId: 1,
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-02"),
      summary: {
        platformSettlements: {},
        netPayout: "0",
      },
    };

    const result = await groupSettlementData(
      mockPrismaFunctions as unknown as TransactionClient,
      groupData as GroupedSettlements
    );

    expect(result.summary.netPayout).toBe("860");
    expect(result.summary.platformSettlements[platformCode]).toEqual({
      isAdjusted: true,
      isNonZero: true,
    });
  });

  it("should calculate negative netPayout correctly for payOut platforms when customer is a Merchant", async () => {
    const platformCode = payOutGroup[0];
    const mockPrismaFunctions = {
      customerSettlements: {
        findMany: vi.fn().mockResolvedValueOnce([
          {
            fromDate: new Date("2025-01-01"),
            toDate: new Date("2025-01-02"),
            customerSettlementsId: 101,
            customerId: 1,
            transactionCount: 5,
            totalTransactionAmount: new Decimal(500),
            refundCount: 1,
            totalRefundAmount: new Decimal(20),
            gatewayFee: new Decimal(5),
            transactionFee: new Decimal(10),
            salesFee: new Decimal(15),
            refundFee: new Decimal(3),
            totalFailedAmount: new Decimal(8),
            total2FaRejectAmount: new Decimal(0),
            total2FaRejectCount: 0,
            txnAmountRTO_R: 0,
            txnCountETI_R1: 0,
            minimumFeeTotal: new Decimal(12),
            minimumFeeCount: 0,
            totalMinimumAmount: new Decimal(0),
            partialReturnAmountRTO: 0,
            partialReturnCountRTO: 0,
            platform: { platformCode },
            customerSettlementAdjustments: [{ amount: new Decimal(7) }],
            customer: {
              customerName: "Merchant B",
              serviceNumber: "002",
              enabled: true,
            },
            customerCustomerType: {
              customerCustomerTypeId: 2,
              customerType: { customerTypeName: "Merchant" },
            },
          },
        ]),
      },
    };

    const groupData = {
      customerId: 1,
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-02"),
      summary: {
        platformSettlements: {},
        netPayout: "0",
      },
    };

    const result = await groupSettlementData(
      mockPrismaFunctions as unknown as TransactionClient,
      groupData as GroupedSettlements
    );

    expect(result.summary.netPayout).toBe("564");
    expect(
      result.summary.platformSettlements[platformCode as PlatformCode]
    ).toEqual({
      isAdjusted: true,
      isNonZero: true,
    });
  });

  it("should calculate positive netPayout correctly for payOut platforms when customer is NonMerchant", async () => {
    const platformCode = payOutGroup[1]; // Second platform from payOutGroup
    const mockPrismaFunctions = {
      customerSettlements: {
        findMany: vi.fn().mockResolvedValueOnce([
          {
            fromDate: new Date("2025-01-01"),
            toDate: new Date("2025-01-02"),
            customerSettlementsId: 102,
            customerId: 1,
            transactionCount: 7,
            totalTransactionAmount: new Decimal(700),
            refundCount: 2,
            totalRefundAmount: new Decimal(40),
            gatewayFee: new Decimal(8),
            transactionFee: new Decimal(12),
            salesFee: new Decimal(18),
            refundFee: new Decimal(4),
            totalFailedAmount: new Decimal(10),
            total2FaRejectAmount: new Decimal(0),
            total2FaRejectCount: 0,
            txnAmountRTO_R: 0,
            txnCountETI_R1: 0,
            minimumFeeTotal: new Decimal(20),
            minimumFeeCount: 0,
            totalMinimumAmount: new Decimal(0),
            partialReturnAmountRTO: 0,
            partialReturnCountRTO: 0,
            platform: { platformCode },
            customerSettlementAdjustments: [{ amount: new Decimal(5) }],
            customer: {
              customerName: "Test Customer",
              serviceNumber: "003",
              enabled: true,
            },
            customerCustomerType: {
              customerCustomerTypeId: 3,
              customerType: { customerTypeName: "TestTypeName" },
            },
          },
        ]),
      },
    };

    const groupData = {
      customerId: 1,
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-02"),
      summary: {
        platformSettlements: {},
        netPayout: "0",
      },
    };

    const result = await groupSettlementData(
      mockPrismaFunctions as unknown as TransactionClient,
      groupData as GroupedSettlements
    );

    expect(result.summary.netPayout).toBe("797");
    expect(
      result.summary.platformSettlements[platformCode as PlatformCode]
    ).toEqual({
      isAdjusted: true,
      isNonZero: true,
    });
  });

  it("should keep netPayout zero and not mark platform adjusted when adjustments are zero", async () => {
    const platformCode = "ETO";
    const mockPrismaFunctions = {
      customerSettlements: {
        findMany: vi.fn().mockResolvedValueOnce([
          {
            fromDate: new Date("2025-01-01"),
            toDate: new Date("2025-01-02"),
            customerSettlementsId: 103,
            customerId: 1,
            transactionCount: 0,
            totalTransactionAmount: new Decimal(0),
            refundCount: 0,
            totalRefundAmount: new Decimal(0),
            gatewayFee: new Decimal(0),
            transactionFee: new Decimal(0),
            salesFee: new Decimal(0),
            refundFee: new Decimal(0),
            totalFailedAmount: new Decimal(0),
            total2FaRejectAmount: new Decimal(0),
            total2FaRejectCount: 0,
            txnAmountRTO_R: 0,
            txnCountETI_R1: 0,
            minimumFeeTotal: new Decimal(0),
            minimumFeeCount: 0,
            totalMinimumAmount: new Decimal(0),
            partialReturnAmountRTO: 0,
            partialReturnCountRTO: 0,
            platform: { platformCode },
            customerSettlementAdjustments: [{ amount: new Decimal(0) }],
            customer: {
              customerName: "No Adjust",
              serviceNumber: "004",
              enabled: true,
            },
            customerCustomerType: {
              customerCustomerTypeId: 1,
              customerType: { customerTypeName: "Merchant" },
            },
          },
        ]),
      },
    };

    const groupData = {
      customerId: 1,
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-02"),
      summary: {
        platformSettlements: {},
        netPayout: "0",
      },
    };

    const result = await groupSettlementData(
      mockPrismaFunctions as unknown as TransactionClient,
      groupData as GroupedSettlements
    );

    expect(result.summary.platformSettlements[platformCode]).toEqual({
      isAdjusted: false,
      isNonZero: false,
    });
    expect(result.summary.netPayout).toBe("0");
  });

  it("should apply summary adjustments when SUMMARY exists", async () => {
    const adjustmentAmount = new Decimal(50);

    vi.mocked(getSettlementStateAndMessage).mockResolvedValue({
      state: "Skipped",
      message: "Summary adjustment only",
    });

    const mockPrismaFunctions = {
      customerSettlements: {
        findMany: vi.fn().mockResolvedValueOnce([
          {
            fromDate: new Date("2025-01-01"),
            toDate: new Date("2025-01-02"),
            customerSettlementsId: 200,
            customerId: 1,
            transactionCount: 0,
            totalTransactionAmount: new Decimal(0),
            refundCount: 0,
            totalRefundAmount: new Decimal(0),
            gatewayFee: new Decimal(0),
            transactionFee: new Decimal(0),
            salesFee: new Decimal(0),
            refundFee: new Decimal(0),
            totalFailedAmount: new Decimal(0),
            total2FaRejectAmount: new Decimal(0),
            total2FaRejectCount: 0,
            txnAmountRTO_R: 0,
            txnCountETI_R1: 0,
            minimumFeeTotal: new Decimal(0),
            minimumFeeCount: 0,
            totalMinimumAmount: new Decimal(0),
            partialReturnAmountRTO: 0,
            partialReturnCountRTO: 0,
            platform: { platformCode: "SUMMARY" as PlatformCode },
            customerSettlementAdjustments: [{ amount: adjustmentAmount }],
            customer: {
              customerName: "test",
              serviceNumber: "12345",
              enabled: true,
            },
            customerCustomerType: {
              customerCustomerTypeId: 1,
              customerType: { customerTypeName: "Merchant" },
            },
          },
        ]),
      },
    };

    const groupData = {
      customerId: 1,
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-02"),
      summary: {
        platformSettlements: {},
        netPayout: "0",
      },
    } as unknown as GroupedSettlements;

    const result = await groupSettlementData(
      mockPrismaFunctions as unknown as TransactionClient,
      groupData
    );

    expect(result.summary.netPayout).toBe("-50");
    expect(result.summary.platformSettlements.SUMMARY).toEqual({
      isAdjusted: true,
      isNonZero: false,
    });
    expect(result.summary.error).toBe("Summary adjustment only");
  });
});
