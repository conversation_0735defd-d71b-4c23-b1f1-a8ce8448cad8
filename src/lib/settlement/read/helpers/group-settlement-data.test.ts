import { Decimal } from "@prisma/client/runtime/library";

import { getSettlementStateAndMessage } from "./get-settlement-state-and-message";
import { groupSettlementData } from "./group-settlement-data";

vi.mock("./get-settlement-state-and-message", () => ({
  getSettlementStateAndMessage: vi.fn().mockReturnValue({
    state: "Skipped",
    message: "No transactions found",
  }),
}));

describe("groupSettlementData", () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should group settlement data correctly", async () => {
    const mockPrismaFunctions = {
      customerSettlements: {
        findMany: vi.fn().mockResolvedValueOnce([
          {
            fromDate: new Date("2025-01-01"),
            toDate: new Date("2025-01-02"),
            customerSettlementsId: 123,
            customerId: 1,
            transactionCount: 123,
            totalTransactionAmount: new Decimal(100),
            refundCount: 1,
            totalRefundAmount: new Decimal(1),
            gatewayFee: new Decimal(0),
            transactionFee: new Decimal(0),
            salesFee: new Decimal(0),
            refundFee: new Decimal(0),
            totalFailedAmount: new Decimal(0),
            total2FaRejectAmount: new Decimal(0),
            total2FaRejectCount: 0,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            txnAmountRTO_R: 0,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            txnCountETI_R1: 0,
            minimumFeeTotal: new Decimal(0),
            minimumFeeCount: 0,
            totalMinimumAmount: new Decimal(0),
            // eslint-disable-next-line @typescript-eslint/naming-convention
            partialReturnAmountRTO: 0,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            partialReturnCountRTO: 0,
            platform: {
              platformCode: "SUMMARY",
            },
            customerSettlementAdjustments: [{ amount: new Decimal(0) }],
            customer: {
              customerName: "test",
              serviceNumber: "12345",
              enabled: true,
            },
            customerCustomerType: {
              customerCustomerTypeId: 1,
              customerType: {
                customerTypeName: "Merchant",
              },
            },
          },
          {
            fromDate: new Date("2025-01-01"),
            toDate: new Date("2025-01-02"),
            customerSettlementsId: 1234,
            customerId: 1,
            transactionCount: 123,
            totalTransactionAmount: new Decimal(10),
            refundCount: 1,
            totalRefundAmount: new Decimal(1),
            gatewayFee: new Decimal(0),
            transactionFee: new Decimal(0),
            salesFee: new Decimal(0),
            refundFee: new Decimal(0),
            totalFailedAmount: new Decimal(0),
            total2FaRejectAmount: new Decimal(0),
            total2FaRejectCount: 0,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            txnAmountRTO_R: 0,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            txnCountETI_R1: 0,
            minimumFeeTotal: new Decimal(0),
            minimumFeeCount: 0,
            totalMinimumAmount: new Decimal(0),
            // eslint-disable-next-line @typescript-eslint/naming-convention
            partialReturnAmountRTO: 0,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            partialReturnCountRTO: 0,
            platform: {
              platformCode: "RTO",
            },
            customerSettlementAdjustments: [{ amount: new Decimal(1) }],
            customer: {
              customerName: "test",
              serviceNumber: "12345",
              enabled: true,
            },
            customerCustomerType: {
              customerCustomerTypeId: 1,
              customerType: {
                customerTypeName: null,
              },
            },
          },
          {
            fromDate: new Date("2025-01-01"),
            toDate: new Date("2025-01-02"),
            customerSettlementsId: 1235,
            customerId: 1,
            transactionCount: 123,
            totalTransactionAmount: new Decimal(100),
            refundCount: 1,
            totalRefundAmount: new Decimal(1),
            gatewayFee: new Decimal(0),
            transactionFee: new Decimal(0),
            salesFee: new Decimal(0),
            refundFee: new Decimal(0),
            totalFailedAmount: new Decimal(0),
            total2FaRejectAmount: new Decimal(0),
            total2FaRejectCount: 0,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            txnAmountRTO_R: 0,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            txnCountETI_R1: 0,
            minimumFeeTotal: new Decimal(0),
            minimumFeeCount: 0,
            totalMinimumAmount: new Decimal(0),
            // eslint-disable-next-line @typescript-eslint/naming-convention
            partialReturnAmountRTO: 0,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            partialReturnCountRTO: 0,
            platform: {
              platformCode: "ETI",
            },
            customerSettlementAdjustments: [{ amount: new Decimal(0) }],
            customer: {
              customerName: "test",
              serviceNumber: "12345",
              enabled: true,
            },
            customerCustomerType: {
              customerCustomerTypeId: 1,
              customerType: {
                customerTypeName: "Merchant",
              },
            },
          },
          {
            fromDate: new Date("2025-01-01"),
            toDate: new Date("2025-01-02"),
            customerSettlementsId: 6,
            customerId: 0,
            transactionCount: 0,
            totalTransactionAmount: new Decimal(0),
            refundCount: 0,
            totalRefundAmount: new Decimal(0),
            gatewayFee: new Decimal(0),
            transactionFee: new Decimal(0),
            salesFee: new Decimal(0),
            refundFee: new Decimal(0),
            totalFailedAmount: new Decimal(0),
            total2FaRejectAmount: new Decimal(0),
            total2FaRejectCount: 0,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            txnAmountRTO_R: 0,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            txnCountETI_R1: 0,
            minimumFeeTotal: new Decimal(0),
            minimumFeeCount: 0,
            totalMinimumAmount: new Decimal(0),
            // eslint-disable-next-line @typescript-eslint/naming-convention
            partialReturnAmountRTO: 0,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            partialReturnCountRTO: 0,
            platform: {
              platformCode: "ETO",
            },
            customerSettlementAdjustments: [{ amount: new Decimal(0) }],
            customer: {
              customerName: "test",
              serviceNumber: "12345",
              enabled: true,
            },
            customerCustomerType: {
              customerCustomerTypeId: 1,
              customerType: {
                customerTypeName: "Merchant",
              },
            },
          },
          {
            fromDate: new Date("2025-01-01"),
            toDate: new Date("2025-01-02"),
            customerSettlementsId: 7,
            customerId: 0,
            transactionCount: 0,
            totalTransactionAmount: new Decimal(0),
            refundCount: 0,
            totalRefundAmount: new Decimal(0),
            gatewayFee: new Decimal(0),
            transactionFee: new Decimal(0),
            salesFee: new Decimal(0),
            refundFee: new Decimal(0),
            totalFailedAmount: new Decimal(0),
            total2FaRejectAmount: new Decimal(0),
            total2FaRejectCount: 0,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            txnAmountRTO_R: 0,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            txnCountETI_R1: 0,
            minimumFeeTotal: new Decimal(0),
            minimumFeeCount: 0,
            totalMinimumAmount: new Decimal(0),
            // eslint-disable-next-line @typescript-eslint/naming-convention
            partialReturnAmountRTO: 0,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            partialReturnCountRTO: 0,
            platform: {
              platformCode: "ACH",
            },
            customerSettlementAdjustments: [{ amount: new Decimal(5) }],
            customer: {
              customerName: "test",
              serviceNumber: "12345",
              enabled: true,
            },
            customerCustomerType: {
              customerCustomerTypeId: 1,
              customerType: {
                customerTypeName: "Merchant",
              },
            },
          },
        ]),
      },
    };

    const groupData = {
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-02"),
      customerId: "1",
      summary: {
        platformSettlements: {},
      },
    };

    const result = await groupSettlementData(
      // @ts-expect-error mocking the prisma functions
      { ...mockPrismaFunctions },
      groupData
    );

    expect(result).toEqual({
      customerId: "1",
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-02"),
      summary: {
        id: "123",
        customerName: "test",
        serviceNumber: "12345",
        customerType: "Merchant",
        status: "Skipped",
        netPayout: "0",
        fromDate: "2025-01-01",
        toDate: "2025-01-02",
        platformSettlements: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          SUMMARY: {
            isAdjusted: false,
          },
          // eslint-disable-next-line @typescript-eslint/naming-convention
          RTO: {
            isAdjusted: true,
          },
          // eslint-disable-next-line @typescript-eslint/naming-convention
          ETI: {
            isAdjusted: false,
          },
          // eslint-disable-next-line @typescript-eslint/naming-convention
          ACH: {
            isAdjusted: true,
          },
        },
        error: "No transactions found",
      },
    });
  });

  it("should group settlement data correctly with a merchant type unknown", async () => {
    const mockPrismaFunctions = {
      customerSettlements: {
        findMany: vi.fn().mockResolvedValueOnce([
          {
            fromDate: new Date("2025-01-01"),
            toDate: new Date("2025-01-02"),
            customerSettlementsId: 123,
            customerId: 1,
            transactionCount: 123,
            totalTransactionAmount: new Decimal(100),
            refundCount: 1,
            totalRefundAmount: new Decimal(1),
            gatewayFee: new Decimal(0),
            transactionFee: new Decimal(0),
            salesFee: new Decimal(0),
            refundFee: new Decimal(0),
            totalFailedAmount: new Decimal(0),
            total2FaRejectAmount: new Decimal(0),
            total2FaRejectCount: 0,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            txnAmountRTO_R: 0,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            txnCountETI_R1: 0,
            minimumFeeTotal: new Decimal(0),
            minimumFeeCount: 0,
            totalMinimumAmount: new Decimal(0),
            // eslint-disable-next-line @typescript-eslint/naming-convention
            partialReturnAmountRTO: 0,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            partialReturnCountRTO: 0,
            platform: {
              platformCode: "SUMMARY",
            },
            customerSettlementAdjustments: [{ amount: new Decimal(0) }],
            customer: {
              customerName: "test",
              serviceNumber: "12345",
              enabled: true,
            },
            customerCustomerType: {
              customerCustomerTypeId: 1,
              customerType: {
                customerTypeName: null,
              },
            },
          },
        ]),
      },
    };

    const groupData = {
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-02"),
      customerId: "1",
      summary: {
        platformSettlements: {},
      },
    };

    const result = await groupSettlementData(
      // @ts-expect-error mocking the prisma functions
      { ...mockPrismaFunctions },
      groupData
    );

    expect(result).toEqual({
      customerId: "1",
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-02"),
      summary: {
        id: "123",
        customerName: "test",
        serviceNumber: "12345",
        customerType: "Unknown",
        status: "Skipped",
        netPayout: "0",
        fromDate: "2025-01-01",
        toDate: "2025-01-02",
        platformSettlements: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          SUMMARY: {
            isAdjusted: false,
          },
        },
        error: "No transactions found",
      },
    });
  });

  it("should group settlement data correctly with no message", async () => {
    vi.mocked(getSettlementStateAndMessage).mockResolvedValue({
      state: "Skipped",
    });

    const mockPrismaFunctions = {
      customerSettlements: {
        findMany: vi.fn().mockResolvedValueOnce([
          {
            fromDate: new Date("2025-01-01"),
            toDate: new Date("2025-01-02"),
            customerSettlementsId: 123,
            customerId: 1,
            transactionCount: 123,
            totalTransactionAmount: new Decimal(100),
            refundCount: 1,
            totalRefundAmount: new Decimal(1),
            gatewayFee: new Decimal(0),
            transactionFee: new Decimal(0),
            salesFee: new Decimal(0),
            refundFee: new Decimal(0),
            totalFailedAmount: new Decimal(0),
            total2FaRejectAmount: new Decimal(0),
            total2FaRejectCount: 0,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            txnAmountRTO_R: 0,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            txnCountETI_R1: 0,
            minimumFeeTotal: new Decimal(0),
            minimumFeeCount: 0,
            totalMinimumAmount: new Decimal(0),
            // eslint-disable-next-line @typescript-eslint/naming-convention
            partialReturnAmountRTO: 0,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            partialReturnCountRTO: 0,
            platform: {
              platformCode: "SUMMARY",
            },
            customerSettlementAdjustments: [{ amount: new Decimal(0) }],
            customer: {
              customerName: "test",
              serviceNumber: "12345",
              enabled: true,
            },
            customerCustomerType: {
              customerCustomerTypeId: 1,
              customerType: {
                customerTypeName: null,
              },
            },
          },
        ]),
      },
    };

    const groupData = {
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-02"),
      customerId: "1",
      summary: {
        platformSettlements: {},
      },
    };

    const result = await groupSettlementData(
      // @ts-expect-error mocking the prisma functions
      { ...mockPrismaFunctions },
      groupData
    );

    expect(result).toEqual({
      customerId: "1",
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-02"),
      summary: {
        id: "123",
        customerName: "test",
        serviceNumber: "12345",
        customerType: "Unknown",
        status: "Skipped",
        netPayout: "0",
        fromDate: "2025-01-01",
        toDate: "2025-01-02",
        platformSettlements: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          SUMMARY: {
            isAdjusted: false,
          },
        },
      },
    });
  });
});
