import { type Prisma } from "@prisma/client";
import { Decimal } from "@prisma/client/runtime/library";

export type Settlement = Prisma.customerSettlementsGetPayload<{
  select: {
    fromDate: true;
    toDate: true;
    customerSettlementsId: true;
    customerId: true;
    transactionCount: true;
    totalTransactionAmount: true;
    refundCount: true;
    totalRefundAmount: true;
    gatewayFee: true;
    transactionFee: true;
    salesFee: true;
    refundFee: true;
    totalFailedAmount: true;
    total2FaRejectAmount: true;
    total2FaRejectCount: true;
    txnAmountRTO_R: true;
    txnCountETI_R1: true;
    minimumFeeTotal: true;
    minimumFeeCount: true;
    totalMinimumAmount: true;
    partialReturnAmountRTO: true;
    partialReturnCountRTO: true;
    platform: {
      select: {
        platformCode: true;
      };
    };
    customerSettlementAdjustments: {
      select: {
        amount: true;
      };
    };
    customer: {
      select: {
        customerName: true;
        serviceNumber: true;
        enabled: true;
      };
    };
    customerCustomerType: {
      select: {
        customerCustomerTypeId: true;
        customerType: {
          select: {
            customerTypeName: true;
          };
        };
      };
    };
  };
}>;

export const settlementValuesAreNotNullOrZeroOrUndefined = (
  settlement: Settlement,
  keys: string[]
) => {
  return keys.some((key) => {
    const value = settlement[key as keyof Settlement];

    if (value === null || value === undefined) {
      return false;
    }

    if (value instanceof Decimal) {
      return !value.isZero(); // False if Decimal(0)
    }

    return value !== 0;
  });
};
