import {
  getAllSettlementsByCustomerAndPeriod,
  getSettlementById,
} from "@lib/settlement/repository/settlement-frequency";
import { dateToString } from "@utils/date-only";
import { objectValuesAreNotNullOrZeroOrUndefined } from "@utils/object-values-are-not-null-or-zero-or-undefined";

import { getAuthToken } from "../../../../../services/blusky/authentication";
import { getEndBalance } from "../../../../../services/blusky/end-balance";
import { calculatePayouts } from "../../helpers/calculate-totals";
import { getSettlementStateAndMessage } from "../../helpers/get-settlement-state-and-message";

import type {
  PlatformCode,
  PlatformSettlements,
  SettlementSummary,
} from "@lib/settlement/repository/types";
import type { PrismaClient } from "@prisma/client";

export const getSettlementData = async (
  id: number,
  prisma: PrismaClient
): Promise<SettlementSummary | undefined> => {
  const settlement = await getSettlementById(id, prisma);

  if (!settlement) {
    return;
  }

  const {
    customerId,
    fromDate,
    toDate,
    customerName,
    serviceNumber,
    customerType,
    customerCustomerTypeId,
  } = settlement;

  const settlements = await getAllSettlementsByCustomerAndPeriod(
    customerId,
    fromDate,
    toDate,
    prisma
  );

  const { state, message } = await getSettlementStateAndMessage(
    {
      customerCustomerTypeId,
      fromDate,
      toDate,
    },
    prisma
  );

  const bluskyToken = await getAuthToken();
  const endBalanceResponse = await getEndBalance([serviceNumber], bluskyToken);

  const endBalance = endBalanceResponse[serviceNumber]?.endBalance
    ? String(endBalanceResponse[serviceNumber]?.endBalance)
    : "Not Found";

  // Transform the array of settlements into one object
  const platformSettlements = Object.assign(
    {},
    ...settlements
  ) as PlatformSettlements;

  const settlementArray = [];

  for (const platformCode of Object.keys(
    platformSettlements
  ) as PlatformCode[]) {
    const details = platformSettlements[platformCode]!;

    details.isNonZero = objectValuesAreNotNullOrZeroOrUndefined(details, [
      "transactionCount",
      "totalTransactionAmount",
      "refundCount",
      "totalRefundAmount",
      "gatewayFee",
      "transactionFee",
      "salesFee",
      "refundFee",
      "totalFailedAmount",
      "total2FaRejectAmount",
      "total2FaRejectCount",
      "txnAmountRTO_R",
      "txnCountETI_R1",
      "minimumFeeTotal",
      "minimumFeeCount",
      "totalMinimumAmount",
      "partialReturnAmountRTO",
      "partialReturnCountRTO",
    ]);

    // None of these properties can be null/undefined as
    // getAllSettlementsByCustomerAndPeriod() guarantees their presence.
    settlementArray.push({
      platformCode,
      totalTransactionAmount: details.totalTransactionAmount!,
      totalFailedAmount: details.totalFailedAmount!,
      transactionFee: details.transactionFee!,
      salesFee: details.salesFee!,
      minimumFeeTotal: details.minimumFeeTotal!,
      gatewayFee: details.gatewayFee!,
      refundFee: details.refundFee!,
      totalRefundAmount: details.totalRefundAmount!,
      adjustments: details.adjustments!,
    });
  }

  const { totalPayout, netPayout, platformTotals } = await calculatePayouts(
    customerType ?? "Unknown",
    settlementArray
  );

  for (const platformCode of Object.keys(platformTotals) as PlatformCode[]) {
    const totals = platformTotals[platformCode];

    platformSettlements[platformCode] = {
      ...platformSettlements[platformCode],
      ...totals,
    };
  }

  platformSettlements.SUMMARY!.totalPayout = totalPayout;
  platformSettlements.SUMMARY!.netPayout = netPayout;

  const result: SettlementSummary = {
    id: id.toString(),
    customerName,
    serviceNumber,
    customerType: customerType ?? "Unknown",
    status: state,
    netPayout: netPayout.toString(),
    endBalance,
    fromDate: dateToString(fromDate),
    toDate: dateToString(toDate),
    platformSettlements,
  };

  if (message) {
    result.error = message;
  }

  return result;
};
