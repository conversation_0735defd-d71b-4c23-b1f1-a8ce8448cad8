/* eslint-disable @typescript-eslint/naming-convention */
import {
  getAllSettlementsByCustomerAndPeriod,
  getSettlementById,
} from "@lib/settlement/repository/settlement-frequency";

import { getSettlementData } from "./get-settlement-data";
import { getEndBalance } from "../../../../../services/blusky/end-balance";
import { calculatePayouts } from "../../helpers/calculate-totals";
import { getSettlementStateAndMessage } from "../../helpers/get-settlement-state-and-message";

import type { PrismaClient } from "@prisma/client";

vi.mock("@lib/settlement/repository/settlement-frequency", () => ({
  getSettlementById: vi.fn(),
  getAllSettlementsByCustomerAndPeriod: vi.fn(),
}));
vi.mock("../../helpers/calculate-totals", () => ({
  calculatePayouts: vi.fn(),
}));
vi.mock("../../../../../services/blusky/authentication", () => ({
  getAuthToken: vi.fn(),
}));
vi.mock("../../../../../services/blusky/end-balance", () => ({
  getEndBalance: vi.fn(),
}));
vi.mock("../../helpers/get-settlement-state-and-message", () => ({
  getSettlementStateAndMessage: vi.fn(),
}));

const mockPrisma = {} as unknown as PrismaClient;

const settlementMerchant = {
  id: 1,
  customerId: 1,
  fromDate: new Date("2024-01-01"),
  toDate: new Date("2024-01-31"),
  customerName: "Test Customer",
  serviceNumber: "123",
  customerType: "Merchant",
  customerCustomerTypeId: 2,
};

const settlementNonMerchant = {
  id: 2,
  customerId: 2,
  fromDate: new Date("2024-01-01"),
  toDate: new Date("2024-01-31"),
  customerName: "Test Customer Non-Merchant",
  serviceNumber: "456",
  customerType: "Integrator",
  customerCustomerTypeId: 4,
};

const platformSettlements = [
  {
    SUMMARY: {
      transactionCount: 0,
      totalTransactionAmount: 0,
      refundCount: 0,
      totalRefundAmount: 0,
      gatewayFee: 0,
      transactionFee: 0,
      salesFee: 0,
      refundFee: 0,
      totalFailedAmount: 0,
      endBalance: -2221,
      total2FaRejectAmount: 0,
      total2FaRejectCount: 0,
      txnAmountRTO_R: 0,
      txnCountETI_R1: 0,
      minimumFeeTotal: 0,
      minimumFeeCount: 0,
      totalMinimumAmount: 0,
      partialReturnAmountRTO: 0,
      partialReturnCountRTO: 0,
      isAdjusted: true,
      adjustments: [
        {
          label: "test",
          amount: 100,
          displayCommentExcel: false,
          comment: "",
        },
      ],
    },
  },
  {
    RFM: {
      transactionCount: 5,
      totalTransactionAmount: 120,
      refundCount: 0,
      totalRefundAmount: 0,
      gatewayFee: 0,
      transactionFee: 3,
      salesFee: 12,
      refundFee: 0,
      totalFailedAmount: 0,
      endBalance: 0,
      total2FaRejectAmount: 0,
      total2FaRejectCount: 0,
      txnAmountRTO_R: 0,
      txnCountETI_R1: 0,
      minimumFeeTotal: 5,
      minimumFeeCount: 1,
      totalMinimumAmount: 10,
      partialReturnAmountRTO: 0,
      partialReturnCountRTO: 0,
      isAdjusted: true,
      adjustments: [
        {
          label: "test",
          amount: 5,
          displayCommentExcel: false,
          comment: "",
        },
      ],
    },
  },
  {
    KYC: {
      transactionCount: 210,
      totalTransactionAmount: 2331,
      refundCount: 0,
      totalRefundAmount: 0,
      gatewayFee: 0,
      transactionFee: 0,
      salesFee: 0,
      refundFee: 0,
      totalFailedAmount: 0,
      endBalance: 0,
      total2FaRejectAmount: 0,
      total2FaRejectCount: 0,
      txnAmountRTO_R: 0,
      txnCountETI_R1: 0,
      minimumFeeTotal: 0,
      minimumFeeCount: 0,
      totalMinimumAmount: 0,
      partialReturnAmountRTO: 0,
      partialReturnCountRTO: 0,
      isAdjusted: true,
      adjustments: [
        {
          label: "test",
          amount: -50,
          displayCommentExcel: false,
          comment: "",
        },
      ],
      kycDetails: {
        KY1: {
          transactionCount: 10,
          totalTransactionAmount: 111,
        },
        KY2: {
          transactionCount: 20,
          totalTransactionAmount: 222,
        },
        KY3: {
          transactionCount: 30,
          totalTransactionAmount: 333,
        },
        KY4: {
          transactionCount: 40,
          totalTransactionAmount: 444,
        },
        KY5: {
          transactionCount: 50,
          totalTransactionAmount: 555,
        },
        KY6: {
          transactionCount: 60,
          totalTransactionAmount: 666,
        },
      },
    },
  },
];

const expectedPlatformSettlementsMerchant = {
  SUMMARY: {
    isNonZero: false,
    transactionCount: 0,
    totalTransactionAmount: 0,
    refundCount: 0,
    totalRefundAmount: 0,
    gatewayFee: 0,
    transactionFee: 0,
    salesFee: 0,
    refundFee: 0,
    totalFailedAmount: 0,
    endBalance: -2221,
    total2FaRejectAmount: 0,
    total2FaRejectCount: 0,
    txnAmountRTO_R: 0,
    txnCountETI_R1: 0,
    minimumFeeTotal: 0,
    minimumFeeCount: 0,
    totalMinimumAmount: 0,
    partialReturnAmountRTO: 0,
    partialReturnCountRTO: 0,
    isAdjusted: true,
    adjustments: [
      {
        label: "test",
        amount: 100,
        displayCommentExcel: false,
        comment: "",
      },
    ],
    totalCosts: 0,
    totalPayable: 0,
    totalAdjustments: 100,
    totalPayout: -2176,
    netPayout: -2276,
  },
  RFM: {
    isNonZero: true,
    transactionCount: 5,
    totalTransactionAmount: 120,
    refundCount: 0,
    totalRefundAmount: 0,
    gatewayFee: 0,
    transactionFee: 3,
    salesFee: 12,
    refundFee: 0,
    totalFailedAmount: 0,
    endBalance: 0,
    total2FaRejectAmount: 0,
    total2FaRejectCount: 0,
    txnAmountRTO_R: 0,
    txnCountETI_R1: 0,
    minimumFeeTotal: 5,
    minimumFeeCount: 1,
    totalMinimumAmount: 10,
    partialReturnAmountRTO: 0,
    partialReturnCountRTO: 0,
    isAdjusted: true,
    adjustments: [
      {
        label: "test",
        amount: 5,
        displayCommentExcel: false,
        comment: "",
      },
    ],
    totalCosts: 20,
    totalPayable: 105,
    totalAdjustments: 5,
  },
  KYC: {
    isNonZero: true,
    transactionCount: 210,
    totalTransactionAmount: 2331,
    refundCount: 0,
    totalRefundAmount: 0,
    gatewayFee: 0,
    transactionFee: 0,
    salesFee: 0,
    refundFee: 0,
    totalFailedAmount: 0,
    endBalance: 0,
    total2FaRejectAmount: 0,
    total2FaRejectCount: 0,
    txnAmountRTO_R: 0,
    txnCountETI_R1: 0,
    minimumFeeTotal: 0,
    minimumFeeCount: 0,
    totalMinimumAmount: 0,
    partialReturnAmountRTO: 0,
    partialReturnCountRTO: 0,
    isAdjusted: true,
    adjustments: [
      {
        label: "test",
        amount: -50,
        displayCommentExcel: false,
        comment: "",
      },
    ],
    kycDetails: {
      KY1: {
        transactionCount: 10,
        totalTransactionAmount: 111,
      },
      KY2: {
        transactionCount: 20,
        totalTransactionAmount: 222,
      },
      KY3: {
        transactionCount: 30,
        totalTransactionAmount: 333,
      },
      KY4: {
        transactionCount: 40,
        totalTransactionAmount: 444,
      },
      KY5: {
        transactionCount: 50,
        totalTransactionAmount: 555,
      },
      KY6: {
        transactionCount: 60,
        totalTransactionAmount: 666,
      },
    },
    totalCosts: 0,
    totalPayable: 2281,
    totalAdjustments: -50,
  },
};

const expectedPlatformSettlementsNonMerchant = {
  SUMMARY: {
    isNonZero: false,
    transactionCount: 0,
    totalTransactionAmount: 0,
    refundCount: 0,
    totalRefundAmount: 0,
    gatewayFee: 0,
    transactionFee: 0,
    salesFee: 0,
    refundFee: 0,
    totalFailedAmount: 0,
    endBalance: -2221,
    total2FaRejectAmount: 0,
    total2FaRejectCount: 0,
    txnAmountRTO_R: 0,
    txnCountETI_R1: 0,
    minimumFeeTotal: 0,
    minimumFeeCount: 0,
    totalMinimumAmount: 0,
    partialReturnAmountRTO: 0,
    partialReturnCountRTO: 0,
    isAdjusted: true,
    adjustments: [
      {
        label: "test",
        amount: 100,
        displayCommentExcel: false,
        comment: "",
      },
    ],
    totalCosts: 0,
    totalPayable: 0,
    totalAdjustments: 100,
    totalPayout: 2386,
    netPayout: 2286,
  },
  RFM: {
    isNonZero: true,
    transactionCount: 5,
    totalTransactionAmount: 120,
    refundCount: 0,
    totalRefundAmount: 0,
    gatewayFee: 0,
    transactionFee: 3,
    salesFee: 12,
    refundFee: 0,
    totalFailedAmount: 0,
    endBalance: 0,
    total2FaRejectAmount: 0,
    total2FaRejectCount: 0,
    txnAmountRTO_R: 0,
    txnCountETI_R1: 0,
    minimumFeeTotal: 5,
    minimumFeeCount: 1,
    totalMinimumAmount: 10,
    partialReturnAmountRTO: 0,
    partialReturnCountRTO: 0,
    isAdjusted: true,
    adjustments: [
      {
        label: "test",
        amount: 5,
        displayCommentExcel: false,
        comment: "",
      },
    ],
    totalCosts: 20,
    totalPayable: 105,
    totalAdjustments: 5,
  },
  KYC: {
    isNonZero: true,
    transactionCount: 210,
    totalTransactionAmount: 2331,
    refundCount: 0,
    totalRefundAmount: 0,
    gatewayFee: 0,
    transactionFee: 0,
    salesFee: 0,
    refundFee: 0,
    totalFailedAmount: 0,
    endBalance: 0,
    total2FaRejectAmount: 0,
    total2FaRejectCount: 0,
    txnAmountRTO_R: 0,
    txnCountETI_R1: 0,
    minimumFeeTotal: 0,
    minimumFeeCount: 0,
    totalMinimumAmount: 0,
    partialReturnAmountRTO: 0,
    partialReturnCountRTO: 0,
    isAdjusted: true,
    adjustments: [
      {
        label: "test",
        amount: -50,
        displayCommentExcel: false,
        comment: "",
      },
    ],
    kycDetails: {
      KY1: {
        transactionCount: 10,
        totalTransactionAmount: 111,
      },
      KY2: {
        transactionCount: 20,
        totalTransactionAmount: 222,
      },
      KY3: {
        transactionCount: 30,
        totalTransactionAmount: 333,
      },
      KY4: {
        transactionCount: 40,
        totalTransactionAmount: 444,
      },
      KY5: {
        transactionCount: 50,
        totalTransactionAmount: 555,
      },
      KY6: {
        transactionCount: 60,
        totalTransactionAmount: 666,
      },
    },
    totalCosts: 0,
    totalPayable: 2281,
    totalAdjustments: -50,
  },
};

describe("getSettlementData", () => {
  afterEach(() => {
    vi.resetAllMocks();
  });

  it("returns undefined if settlement not found", async () => {
    const result = await getSettlementData(1, mockPrisma);
    expect(result).toBeUndefined();
  });

  it("returns settlement summary with correct fields for merchant", async () => {
    vi.mocked(getSettlementById).mockResolvedValue(settlementMerchant);
    vi.mocked(getAllSettlementsByCustomerAndPeriod).mockResolvedValue(
      platformSettlements
    );
    vi.mocked(getSettlementStateAndMessage).mockResolvedValue({
      state: "Approval Success",
    });
    vi.mocked(getEndBalance).mockResolvedValue({
      "123": {
        customerName: "Test Customer",
        endBalance: -1234.56,
        currentBalance: -1234.56,
        totalBalance: -1234.56,
      },
    });
    vi.mocked(calculatePayouts).mockResolvedValue({
      totalPayout: -2176,
      netPayout: -2276,
      platformTotals: {
        RFM: {
          totalPayable: 105,
          totalCosts: 20,
          totalAdjustments: 5,
        },
        KYC: {
          totalPayable: 2281,
          totalCosts: 0,
          totalAdjustments: -50,
        },
        SUMMARY: {
          totalPayable: 0,
          totalCosts: 0,
          totalAdjustments: 100,
        },
      },
    });

    const result = await getSettlementData(1, mockPrisma);

    expect(result?.id).toBe("1");
    expect(result?.customerName).toBe("Test Customer");
    expect(result?.serviceNumber).toBe("123");
    expect(result?.customerType).toBe("Merchant");
    expect(result?.status).toBe("Approval Success");
    expect(result?.endBalance).toBe("-1234.56");
    expect(result?.fromDate).toBe("2024-01-01");
    expect(result?.toDate).toBe("2024-01-31");
    expect(result?.platformSettlements).toEqual(
      expectedPlatformSettlementsMerchant
    );
  });

  it("returns settlement summary with correct fields for non-merchant", async () => {
    vi.mocked(getSettlementById).mockResolvedValue(settlementNonMerchant);
    vi.mocked(getAllSettlementsByCustomerAndPeriod).mockResolvedValue(
      platformSettlements
    );
    vi.mocked(getSettlementStateAndMessage).mockResolvedValue({
      state: "Approval Success",
    });
    vi.mocked(getEndBalance).mockResolvedValue({
      "456": {
        customerName: "Test Customer Non-Merchant",
        endBalance: -1234.56,
        currentBalance: -1234.56,
        totalBalance: -1234.56,
      },
    });
    vi.mocked(calculatePayouts).mockResolvedValue({
      totalPayout: 2386,
      netPayout: 2286,
      platformTotals: {
        RFM: {
          totalPayable: 105,
          totalCosts: 20,
          totalAdjustments: 5,
        },
        KYC: {
          totalPayable: 2281,
          totalCosts: 0,
          totalAdjustments: -50,
        },
        SUMMARY: {
          totalPayable: 0,
          totalCosts: 0,
          totalAdjustments: 100,
        },
      },
    });

    const result = await getSettlementData(1, mockPrisma);

    expect(result?.id).toBe("1");
    expect(result?.customerName).toBe("Test Customer Non-Merchant");
    expect(result?.serviceNumber).toBe("456");
    expect(result?.customerType).toBe("Integrator");
    expect(result?.status).toBe("Approval Success");
    expect(result?.endBalance).toBe("-1234.56");
    expect(result?.fromDate).toBe("2024-01-01");
    expect(result?.toDate).toBe("2024-01-31");
    expect(result?.platformSettlements).toEqual(
      expectedPlatformSettlementsNonMerchant
    );
  });

  it("handles missing customerType and customerCustomerType and sets error message", async () => {
    const settlement = {
      id: 1,
      customerId: 1,
      fromDate: new Date("2024-01-01"),
      toDate: new Date("2024-01-31"),
      customerName: "Test Customer",
      serviceNumber: "123",
      customerType: undefined,
      customerCustomerTypeId: undefined,
    };

    vi.mocked(getSettlementById).mockResolvedValue(settlement);
    vi.mocked(getAllSettlementsByCustomerAndPeriod).mockResolvedValue(
      platformSettlements
    );
    vi.mocked(getSettlementStateAndMessage).mockResolvedValue({
      state: "Error",
      message: "customerCustomerTypeId is missing from settlement",
    });
    vi.mocked(getEndBalance).mockResolvedValue({
      "123": {
        customerName: "Test Customer",
        endBalance: -1234.56,
        currentBalance: -1234.56,
        totalBalance: -1234.56,
      },
    });
    vi.mocked(calculatePayouts).mockResolvedValue({
      totalPayout: 2386,
      netPayout: 2286,
      platformTotals: {
        RFM: {
          totalPayable: 105,
          totalCosts: 20,
          totalAdjustments: 5,
        },
        KYC: {
          totalPayable: 2281,
          totalCosts: 0,
          totalAdjustments: -50,
        },
        SUMMARY: {
          totalPayable: 0,
          totalCosts: 0,
          totalAdjustments: 100,
        },
      },
    });

    const result = await getSettlementData(1, mockPrisma);

    expect(result?.id).toBe("1");
    expect(result?.customerName).toBe("Test Customer");
    expect(result?.serviceNumber).toBe("123");
    expect(result?.customerType).toBe("Unknown");
    expect(result?.status).toBe("Error");
    expect(result?.endBalance).toBe("-1234.56");
    expect(result?.fromDate).toBe("2024-01-01");
    expect(result?.toDate).toBe("2024-01-31");
    expect(result?.platformSettlements).toEqual(
      expectedPlatformSettlementsNonMerchant
    );
  });

  it("handles missing endBalance", async () => {
    vi.mocked(getSettlementById).mockResolvedValue(settlementMerchant);
    vi.mocked(getAllSettlementsByCustomerAndPeriod).mockResolvedValue(
      platformSettlements
    );
    vi.mocked(getSettlementStateAndMessage).mockResolvedValue({
      state: "Approval Success",
    });
    vi.mocked(getEndBalance).mockResolvedValue({});
    vi.mocked(calculatePayouts).mockResolvedValue({
      totalPayout: -2176,
      netPayout: -2276,
      platformTotals: {
        RFM: {
          totalPayable: 105,
          totalCosts: 20,
          totalAdjustments: 5,
        },
        KYC: {
          totalPayable: 2281,
          totalCosts: 0,
          totalAdjustments: -50,
        },
        SUMMARY: {
          totalPayable: 0,
          totalCosts: 0,
          totalAdjustments: 100,
        },
      },
    });

    const result = await getSettlementData(1, mockPrisma);

    expect(result?.id).toBe("1");
    expect(result?.customerName).toBe("Test Customer");
    expect(result?.serviceNumber).toBe("123");
    expect(result?.customerType).toBe("Merchant");
    expect(result?.status).toBe("Approval Success");
    expect(result?.endBalance).toBe("Not Found");
    expect(result?.fromDate).toBe("2024-01-01");
    expect(result?.toDate).toBe("2024-01-31");
    expect(result?.platformSettlements).toEqual(
      expectedPlatformSettlementsMerchant
    );
  });
});
