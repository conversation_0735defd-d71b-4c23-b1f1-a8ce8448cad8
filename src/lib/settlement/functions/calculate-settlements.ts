import { type Customer } from "@lib/customer/repository/types";
import { getFirstTransactionEverDate } from "@lib/settlement/functions/helpers/get-first-transaction-ever-date";
import { type PrismaClient } from "@prisma/client";

import { calculateMerchantSettlement } from "./workers/calculate-merchant-settlement";
import { calculateNonMerchantSettlement } from "./workers/calculate-non-merchant-settlement";
import { type SettlementResult } from "./workers/types";
import { getAuthToken } from "../../../services/blusky/authentication";
import { type CalculationOptions } from "../../../types/settlement";
import { type DateOnly } from "../../../utils/date-only";
import { SettlementError } from "../../../utils/errors/settlement-error";
import {
  getMerchantsSettlementConfiguration,
  getNonMerchantsSettlementConfiguration,
} from "../repository/settlement-configuration";

import type { SettlementsResult } from "../repository/types";

const calculateSettlements = async (
  customers: Customer[],
  interval: { fromDate: DateOnly; toDate: DateOnly },
  prisma: PrismaClient,
  options?: CalculationOptions
): Promise<SettlementsResult> => {
  const merchants = customers.filter(
    (customer) => customer.customerTypeName === "Merchant"
  );

  const nonMerchants = customers.filter(
    (customer) => customer.customerTypeName !== "Merchant"
  );

  const merchantsSettlementConfig = await getMerchantsSettlementConfiguration(
    merchants.map((merchant) => merchant.customerId),
    interval,
    prisma
  );

  const token = await getAuthToken();
  const result: SettlementsResult = {};

  for (const config of merchantsSettlementConfig) {
    // eslint-disable-next-line no-await-in-loop
    result[config.customerCustomerTypeId] = await _runSettlementCalculation(
      async () => {
        const firstTransactionDate = await getFirstTransactionEverDate(
          prisma,
          config,
          token
        );

        return calculateMerchantSettlement(
          { ...config, firstTransactionDate },
          interval,
          token,
          options!,
          prisma
        );
      }
    );
  }

  for (const nonMerchant of nonMerchants) {
    // eslint-disable-next-line no-await-in-loop
    const nonMerchantConfig = await getNonMerchantsSettlementConfiguration(
      nonMerchant.customerId,
      interval,
      prisma
    );

    const { associatedMerchantIds } = nonMerchantConfig;

    const associatedMerchantsSettlementConfig =
      // eslint-disable-next-line no-await-in-loop
      await getMerchantsSettlementConfiguration(
        associatedMerchantIds,
        interval,
        prisma
      );

    // eslint-disable-next-line no-await-in-loop
    const merchantsSettlementConfig = await Promise.all(
      associatedMerchantsSettlementConfig.map(async (config) => {
        const firstTransactionDate = await getFirstTransactionEverDate(
          prisma,
          config,
          token
        );

        return { ...config, firstTransactionDate };
      })
    );

    result[nonMerchant.customerCustomerTypeId] =
      // eslint-disable-next-line no-await-in-loop
      await _runSettlementCalculation(async () => {
        return calculateNonMerchantSettlement(
          {
            nonMerchantSettlementConfig: nonMerchantConfig,
            merchantsSettlementConfig,
          },
          interval,
          token,
          { ...options, includeKyc: false }
        );
      });
  }

  return result;
};

/**
 * Helper function to measure execution time, run a calculation,
 * and handle both success and error cases in a consistent way.
 */
async function _runSettlementCalculation(
  function_: () => Promise<SettlementResult>
) {
  const start = performance.now();

  try {
    const settlement = await function_();
    const timeTakenInSeconds = Number.parseFloat(
      ((performance.now() - start) / 1000).toFixed(2)
    );

    return {
      status: "SUCCESS" as const,
      data: {
        result: settlement,
        timeTakenInSeconds,
      },
    };
  } catch (error) {
    if (error instanceof SettlementError && error.isSkipped) {
      return {
        status: "SKIPPED" as const,
        message: error instanceof Error ? error.message : "Unknown error",
      };
    }

    return {
      status: "ERROR" as const,
      message: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

export { calculateSettlements };
