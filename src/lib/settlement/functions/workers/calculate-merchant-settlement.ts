import { type Platform } from "@constants/transactions/platform";
import { calculateKycRecords } from "@lib/settlement/functions/workers/calculate-kyc";
import { type PrismaClient } from "@prisma/client";

import { type SettlementResult } from "./types";
import {
  getTransactions,
  transactionStatus,
  type Transaction,
} from "../../../../services/blusky/transactions";
import {
  type CalculationOptions,
  type DeterminedInterval,
} from "../../../../types/settlement";
import {
  endOfPreviousMonthOnly,
  fromDateOnly,
  startOfPreviousMonthOnly,
  getHigherDate,
  getLowerDate,
  type DateOnly,
} from "../../../../utils/date-only";
import { SettlementError } from "../../../../utils/errors/settlement-error";
import {
  type MerchantPlatformMember,
  type MerchantSettlementConfiguration,
} from "../../repository/types";
import { areIntervalsValid, areIntervalsContinuous } from "../helpers/checks";
import { combineTotals } from "../helpers/combine-totals";
import {
  calculateFee,
  type ChargedFees,
  sumChargedFees,
} from "../helpers/fees";
import {
  determineMerchantFeeRates,
  type RatesMeta,
  type Rates,
  type RatesWithMeta,
} from "../helpers/rates";
import {
  calculatePayOutTransactionsSummary,
  calculatePayInTransactionsSummary,
  type TransactionsSummary,
  sumTransactionSummaries,
} from "../helpers/transactions-summary";

type RatesDeterminingProfile = {
  fromDate: DateOnly;
  toDate: DateOnly;
  combineTotal: number;
  isCombineIdpTierSet: boolean;
  isCombineAchTierSet: boolean;
  isCombineRtoTierSet: boolean;
  serviceNumbers: string[];
  isCombineMultipleServicesEnabled: boolean;
};

type ChargedFeesMeta = {
  rates: Rates & RatesMeta;
  rateDeterminingProfile: RatesDeterminingProfile;
  transactionSummary: TransactionsSummary;
};

type ChargedFeeWithMeta = {
  chargedFees: ChargedFees;
  meta: ChargedFeesMeta;
  interval: { fromDate: DateOnly; toDate: DateOnly };
};

const calculateMerchantSettlement = async (
  merchantConfig: MerchantSettlementConfiguration,
  interval: DeterminedInterval,
  token: string,
  options: CalculationOptions,
  prisma?: PrismaClient
): Promise<SettlementResult> => {
  _validateMerchantConfigOrThrow(merchantConfig);

  const transactions = await _fetchTransactions(
    merchantConfig,
    interval,
    token,
    options
  );

  const settlementResult: SettlementResult = {
    settlement: {},
    kyc: undefined,
  };

  const firstTransactionEverDate = merchantConfig.firstTransactionDate;

  const combineTotals = await _getRateDeterminingTotals(
    merchantConfig,
    interval,
    token,
    options
  );

  const { totals, meta } = combineTotals;
  const trueUp = _prepareTrueUp(options, combineTotals.trueUp);

  for (const [platform, platformConfigs] of Object.entries(
    merchantConfig.platformConfigurations
  )) {
    // Report on the existence of billable transactions, but no platform configuration
    // was found to calculate the associated fees.
    if (
      platformConfigs.length === 0 &&
      transactions[platform]?.some((transaction) => transaction.billable)
    ) {
      throw new SettlementError(`No platform configuration found for the platform ${platform}, 
          however transactions are available.`);
    }

    if (platformConfigs.length === 0 || !(platform in transactions)) {
      // No platform configuration to calculate the fees or no transactions found for the platform
      continue;
    }

    const platformFeesWithMetaArray: ChargedFeeWithMeta[] = [];
    const trueUpFeesWithMetaArray: ChargedFeeWithMeta[] = [];

    const { platformId } = platformConfigs[0]!;

    _validatePlatformIntervalsOrThrow(
      platformConfigs.map((member) => ({
        fromDate: member.fromDate,
        toDate: member.toDate,
      })),
      platform
    );

    const sortedConfig = platformConfigs.sort(
      (a, b) =>
        new Date(fromDateOnly(a.fromDate!)).getTime() -
        new Date(fromDateOnly(b.fromDate!)).getTime()
    );

    for (const [, platformMember] of sortedConfig.entries()) {
      const configInterval = {
        fromDate: getHigherDate(interval.fromDate, platformMember.fromDate!),
        toDate: getLowerDate(
          platformMember.toDate ?? interval.toDate,
          interval.toDate
        ),
      };

      const fees = _calculatePlatformFee(
        configInterval,
        platformMember,
        {
          ...meta,
          combineTotal: totals[platform] ?? 0,
        } satisfies RatesDeterminingProfile,

        {
          transactions: transactions[platform] ?? [],
          ...(firstTransactionEverDate && { firstTransactionEverDate }),
        }
      );
      platformFeesWithMetaArray.push(fees);

      if (trueUp.isNeeded && trueUp.meta) {
        const trueUpFees = _calculatePlatformFee(
          configInterval,
          platformMember,
          { ...trueUp.meta, combineTotal: trueUp.totals[platform] ?? 0 },

          {
            transactions: transactions[platform] ?? [],
            ...(firstTransactionEverDate && { firstTransactionEverDate }),
          }
        );
        trueUpFeesWithMetaArray.push(trueUpFees);
      }
    }

    settlementResult.settlement[platform] = {
      totalChargedFees: sumChargedFees(
        platformFeesWithMetaArray.map((item) => item.chargedFees)
      ),
      totalTransactionSummary: sumTransactionSummaries(
        platformFeesWithMetaArray.map((item) => item.meta.transactionSummary)
      ),
      platformId,
      isContractChange: sortedConfig.length > 1,
      feesBreakdown: platformFeesWithMetaArray,
      ...(trueUpFeesWithMetaArray.length > 0 && {
        trueUp: {
          totalChargedFees: sumChargedFees(
            trueUpFeesWithMetaArray.map((item) => item.chargedFees)
          ),
          totalTransactionSummary: sumTransactionSummaries(
            trueUpFeesWithMetaArray.map((item) => item.meta.transactionSummary)
          ),
          platformId,
          isContractChange: sortedConfig.length > 1,
          feesBreakdown: trueUpFeesWithMetaArray,
        },
      }),
    };
  }

  if (options?.includeKyc) {
    settlementResult.kyc = await calculateKycRecords(
      merchantConfig,
      interval,
      token,
      prisma!
    );
  }

  return settlementResult;
};

const _validateMerchantConfigOrThrow = (
  merchantConfig: MerchantSettlementConfiguration
) => {
  if (
    !merchantConfig?.platformConfigurations ||
    Object.keys(merchantConfig.platformConfigurations).length === 0
  ) {
    throw new SettlementError("No platform configurations found", true);
  }

  _validateServiceNumbersOrThrow(merchantConfig.customerType.serviceNumbers);
};

const _fetchTransactions = async (
  merchantConfig: MerchantSettlementConfiguration,
  interval: { fromDate: DateOnly; toDate: DateOnly },
  token: string,
  options?: CalculationOptions
) => {
  const transactions = await getTransactions(
    merchantConfig.serviceNumber,
    interval,
    {
      token,
      aggregate: options?.aggregate ?? {
        [transactionStatus.rejected1]: {
          aggregateForPlatforms: ["ETI", "RFM"],
          targetPlatform: "RTO",
          keepInOriginal: false,
        },
      },
    }
  );

  if (!transactions || Object.keys(transactions).length === 0) {
    throw new SettlementError("No transactions found", true);
  }

  return transactions;
};

const _prepareTrueUp = (
  options: CalculationOptions | undefined,
  trueUp?: {
    totals: Record<string, number>;
    meta: Omit<RatesDeterminingProfile, "combineTotal">;
  }
) => {
  return {
    isNeeded: Boolean(options?.trueUp),
    totals: trueUp?.totals ?? {},
    meta: trueUp?.meta,
  };
};

const _getRateDeterminingTotals = async (
  merchantConfig: MerchantSettlementConfiguration,
  interval: { fromDate: DateOnly; toDate: DateOnly },
  token: string,
  options?: CalculationOptions
): Promise<{
  totals: Record<string, number>;
  meta: Omit<RatesDeterminingProfile, "combineTotal">;
  trueUp?: {
    totals: Record<string, number>;
    meta: Omit<RatesDeterminingProfile, "combineTotal">;
  };
}> => {
  const {
    isCombineIdpTierSet,
    isCombineRtoTierSet,
    isCombineAchTierSet,
    serviceNumbers,
  } = merchantConfig.customerType;

  const payInCombinations: Record<Platform, boolean> | Record<string, unknown> =
    {};
  const payOutCombinations:
    | Record<Platform, boolean>
    | Record<string, unknown> = {};

  if (merchantConfig.customerType.volumeCombination.length > 0) {
    const payInVolumeCombination =
      merchantConfig.customerType.volumeCombination.find((vc) => {
        return vc.paymentType.paymentTypeName === "Pay-In";
      });

    const payOutVolumeCombination =
      merchantConfig.customerType.volumeCombination.find((vc) => {
        return vc.paymentType.paymentTypeName === "Pay-Out";
      });

    for (const vcp of payInVolumeCombination!.volumeCombinationPlatform) {
      payInCombinations[vcp.platform.platformCode as Platform] = vcp.enabled;
    }

    for (const vcp of payOutVolumeCombination!.volumeCombinationPlatform) {
      payOutCombinations[vcp.platform.platformCode as Platform] = vcp.enabled;
    }
  }

  const combineAllserviceNumbers = [];
  combineAllserviceNumbers.push(merchantConfig.serviceNumber);

  if (merchantConfig.isCombineMultipleServices) {
    serviceNumbers.map((member) =>
      combineAllserviceNumbers.push(member.serviceNumber)
    );
  }

  const rateDeterminingInterval = {
    fromDate: options?.rateDeterminingInterval
      ? options.rateDeterminingInterval.fromDate
      : startOfPreviousMonthOnly(interval.fromDate),
    toDate: options?.rateDeterminingInterval
      ? options.rateDeterminingInterval.toDate
      : endOfPreviousMonthOnly(interval.fromDate),
  };

  const totals = await combineTotals(
    combineAllserviceNumbers,
    rateDeterminingInterval,
    token,
    {
      legacy: {
        isCombineIdpTierSet,
        isCombineRtoTierSet,
        isCombineAchTierSet,
      },
      payInCombinations,
      payOutCombinations,
    }
  );

  let trueUpTotals;

  if (options?.trueUp?.rateDeterminingInterval) {
    trueUpTotals = await combineTotals(
      combineAllserviceNumbers,
      options.trueUp.rateDeterminingInterval,
      token,
      {
        legacy: {
          isCombineIdpTierSet,
          isCombineRtoTierSet,
          isCombineAchTierSet,
        },
        payInCombinations,
        payOutCombinations,
      }
    );
  }

  return {
    totals,
    meta: {
      isCombineIdpTierSet,
      isCombineRtoTierSet,
      isCombineAchTierSet,
      fromDate: interval.fromDate,
      toDate: interval.toDate,
      serviceNumbers: serviceNumbers.map((member) => member.serviceNumber),
      isCombineMultipleServicesEnabled:
        merchantConfig.isCombineMultipleServices,
    },
    ...(trueUpTotals && {
      trueUp: {
        totals: trueUpTotals,
        meta: {
          isCombineIdpTierSet,
          isCombineRtoTierSet,
          isCombineAchTierSet,
          fromDate: options!.trueUp!.rateDeterminingInterval.fromDate,
          toDate: options!.trueUp!.rateDeterminingInterval.toDate,
          serviceNumbers: serviceNumbers.map((member) => member.serviceNumber),
          isCombineMultipleServicesEnabled:
            merchantConfig.isCombineMultipleServices,
        },
      },
    }),
  };
};

const _getTransactionSummaryForPlatform = (
  interval: {
    fromDate: DateOnly;
    toDate: DateOnly;
  },
  transactions: Transaction[],
  platformType: string,
  ratesWithMeta: RatesWithMeta,
  minimumFeeEligibilityCheck?: (
    transaction: Transaction,
    ratesWithMeta: RatesWithMeta
  ) => boolean
): TransactionsSummary => {
  let transactionSummary: TransactionsSummary;

  switch (platformType) {
    case "Pay-Out": {
      transactionSummary = calculatePayOutTransactionsSummary(
        interval,
        transactions ?? []
      );
      break;
    }

    case "Pay-In": {
      transactionSummary = calculatePayInTransactionsSummary(
        interval,
        transactions ?? [],
        ratesWithMeta,
        minimumFeeEligibilityCheck
      );
      break;
    }

    default: {
      throw new Error(
        `Unsupported platform payment type: ${platformType}. Only Pay-In and Pay-Out are supported`
      );
    }
  }

  return transactionSummary;
};

export const minimumFeeEligibilityCheck = (
  txn: Transaction,
  ratesWithMeta: RatesWithMeta
) => {
  const minFee =
    txn.finalAmt * ratesWithMeta.rates.salesFee +
    ratesWithMeta.rates.transactionFee;

  return ratesWithMeta.rates.minimumThreshold > 0
    ? ratesWithMeta.rates.minimumThreshold > txn.finalAmt
    : ratesWithMeta.rates.minimumCharge > minFee;
};

const _calculatePlatformFee = (
  interval: {
    fromDate: DateOnly;
    toDate: DateOnly;
  },
  platformMember: MerchantPlatformMember,
  rateDeterminingProfile: RatesDeterminingProfile,
  transaction: {
    transactions: Transaction[];
    firstTransactionEverDate?: DateOnly;
  }
) => {
  const ratesWithMeta = determineMerchantFeeRates(
    interval.fromDate,
    rateDeterminingProfile.combineTotal,
    platformMember,
    transaction.firstTransactionEverDate
  );

  const transactionSummary = _getTransactionSummaryForPlatform(
    interval,
    transaction.transactions,
    platformMember.platformType,
    ratesWithMeta,
    ratesWithMeta.meta.isMinimumFeeApplicable
      ? minimumFeeEligibilityCheck
      : undefined
  );

  const chargedFees: ChargedFees = calculateFee(
    transactionSummary,
    ratesWithMeta.rates
  );

  const chargedFeesWithMeta = {
    interval,
    chargedFees,
    meta: {
      rates: { ...ratesWithMeta.rates, ...ratesWithMeta.meta },
      rateDeterminingProfile,
      transactionSummary,
    },
  } satisfies ChargedFeeWithMeta;

  return chargedFeesWithMeta;
};

const _validateServiceNumbersOrThrow = (
  serviceNumbers: Array<{
    serviceNumber: string;
    fromDate: DateOnly | undefined;
    toDate: DateOnly | undefined;
  }>
) => {
  const invalidServiceNumber = serviceNumbers.find(
    (member) => !member.fromDate
  );

  if (invalidServiceNumber) {
    throw new SettlementError(
      `Service number ${invalidServiceNumber.serviceNumber} has an invalid or missing fromDate.`
    );
  }
};

/**
 * Validates and checks the continuity of the intervals.
 *
 * @param {Array<{ fromDate: DateOnly | undefined; toDate: DateOnly | undefined }>} intervals -
 * The array of intervals to validate. Each interval contains a `fromDate` and a `toDate`.
 * @param {string} platform - The name or identifier of the platform.
 * @throws {Error} If the intervals are invalid or not continuous.
 */
const _validatePlatformIntervalsOrThrow = (
  intervals: Array<{
    fromDate: DateOnly | undefined;
    toDate: DateOnly | undefined;
  }>,
  platform: string
) => {
  if (!areIntervalsValid(intervals)) {
    throw new SettlementError(`Invalid intervals for platform ${platform}`);
  }

  if (
    !areIntervalsContinuous(
      intervals.map((member) => ({
        fromDate: member.fromDate!,
        toDate: member.toDate,
      }))
    )
  ) {
    throw new SettlementError(
      `Intervals for platform ${platform} are not continuous`
    );
  }
};

export { calculateMerchantSettlement, type ChargedFeeWithMeta };
