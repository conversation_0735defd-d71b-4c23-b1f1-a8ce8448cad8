import { type PrismaClient } from "@prisma/client";

const getCustomerIdByCustomerCustomerType = async (
  customerCustomerTypeId: number,
  prisma: PrismaClient
): Promise<number | undefined> => {
  const customerCustomerType = await prisma.customerCustomerType.findFirst({
    where: {
      customerCustomerTypeId,
    },
    select: {
      customerId: true,
    },
  });

  return customerCustomerType?.customerId;
};

export { getCustomerIdByCustomerCustomerType };
