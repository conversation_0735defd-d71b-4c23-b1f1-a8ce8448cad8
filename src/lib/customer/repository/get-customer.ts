import { type PrismaClient } from "@prisma/client";

import { customerSelectClause } from "./helpers/customer-select-clause";
import { customerTypeSelectClause } from "./helpers/customer-type-select-clause";
import { flattenCustomer } from "./helpers/flatten-customer";
import { type Customer } from "./types";
import { type NonMerchantType } from "../../settlement/repository/types";

const getAllCustomers = async (
  prisma: PrismaClient,
  type: "Merchant" | "Agent" | "Sub Agent" | "Integrator" | "All" = "All"
): Promise<Customer[]> => {
  const customers = await prisma.customerCustomerType.findMany({
    where: {
      deletedAt: null,
      ...(type !== "All" && {
        customerType: {
          customerTypeName: type,
        },
      }),
    },
    select: {
      customerCustomerTypeId: true,
      statementFolderLocation: true,
      customerType: {
        select: customerTypeSelectClause,
      },
      customer: {
        select: customerSelectClause,
      },
    },
  });

  return customers.map((customer) => flattenCustomer(customer));
};

const getAllCustomersByFrequency = async (
  prisma: PrismaClient,
  frequencyId: number,
  type: "Merchant" | "Agent" | "Sub Agent" | "Integrator" | "All" = "All",
  isActive = true
): Promise<Customer[]> => {
  const customers = await prisma.customerCustomerType.findMany({
    where: {
      deletedAt: null,
      statementFrequencyId: frequencyId,
      ...(type !== "All" && {
        customerType: {
          customerTypeName: type,
        },
      }),
      customer: {
        enabled: isActive,
        deletedAt: null,
      },
    },
    select: {
      customerCustomerTypeId: true,
      statementFolderLocation: true,
      customerType: {
        select: customerTypeSelectClause,
      },
      customer: {
        select: customerSelectClause,
      },
    },
  });

  return customers.map((customer) => flattenCustomer(customer));
};

const getCustomers = async (
  customerCustomerTypeIds: number[],
  prisma: PrismaClient,
  type: "Merchant" | "Agent" | "Sub Agent" | "Integrator" | "All" = "All"
): Promise<Customer[]> => {
  const customers = await prisma.customerCustomerType.findMany({
    where: {
      customerCustomerTypeId: { in: customerCustomerTypeIds },
      deletedAt: null,
      ...(type !== "All" && {
        customerType: {
          customerTypeName: type,
        },
      }),
    },
    select: {
      customerCustomerTypeId: true,
      statementFolderLocation: true,
      customerType: {
        select: customerTypeSelectClause,
      },
      customer: {
        select: customerSelectClause,
      },
    },
  });

  return customers.map((customer) => flattenCustomer(customer));
};

const getAssociatedMerchantIdsForNonMerchant = async (
  nonMerchantId: number,
  type: NonMerchantType,
  prisma: PrismaClient
): Promise<Set<number>> => {
  // Determine the field name based on the type.
  let fieldName:
    | "integratorCustomerId"
    | "agentCustomerId"
    | "subAgentCustomerId";

  switch (type) {
    case "Integrator": {
      fieldName = "integratorCustomerId";
      break;
    }

    case "Agent": {
      fieldName = "agentCustomerId";
      break;
    }

    case "Sub Agent": {
      fieldName = "subAgentCustomerId";
      break;
    }
  }

  const whereClause = {
    deletedAt: null,
    [fieldName]: nonMerchantId,
  };

  const merchantIds = await prisma.merchantPlatform.findMany({
    where: whereClause,
    distinct: ["clientCustomerId"],
    select: {
      clientCustomerId: true,
    },
  });

  return new Set(merchantIds.map((merchant) => merchant.clientCustomerId));
};

export {
  getAllCustomers,
  getAllCustomersByFrequency,
  getCustomers,
  getAssociatedMerchantIdsForNonMerchant,
};
