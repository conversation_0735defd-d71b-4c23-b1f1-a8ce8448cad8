import { type Prisma } from "@prisma/client";

import { type customerSelectClause } from "./customer-select-clause";
import { type customerTypeSelectClause } from "./customer-type-select-clause";
import { type Customer } from "../types";

const flattenCustomer = (
  customerType: Prisma.customerCustomerTypeGetPayload<{
    select: {
      customerCustomerTypeId: true;
      statementFolderLocation: true;
      customerType: {
        select: typeof customerTypeSelectClause;
      };
      customer: {
        select: typeof customerSelectClause;
      };
    };
  }>
): Customer => {
  return {
    customerId: customerType.customer.customerId,
    customerCustomerTypeId: customerType.customerCustomerTypeId,
    serviceNumber: customerType.customer.serviceNumber,
    entityName: customerType.customer.entity.entityName ?? "",
    entityId: customerType.customer.entity.entityId,
    customerTradingName: customerType.customer.customerTradingName ?? "",
    customerName: customerType.customer.customerName,
    statementFolderLocation: customerType.statementFolderLocation ?? "",
    enabled: customerType.customer.enabled,
    customerTypeId: customerType.customerType.customerTypeId,
    customerTypeName: customerType.customerType.customerTypeName,
    countryName: customerType.customer?.country?.countryName ?? undefined,
    countryId: customerType.customer?.country?.countryId,
    provinceName: customerType.customer?.province?.provinceName ?? undefined,
    provinceId: customerType.customer?.province?.provinceId,
    nonMerchantSettlementType:
      customerType.customer?.customerSettlementType?.description ?? undefined,
  } satisfies Customer;
};

export { flattenCustomer };
