import { handler } from "./handler";
import * as transactions from "../../../services/blusky/transactions";

describe("Transaction Handler Tests", () => {
  it("should fetch transactions with SQL", async () => {
    const getTransactionsOptions = {
      aggregate: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        STATUS_REJECTED1: {
          aggregateForPlatforms: ["ETI", "RFM"],
          targetPlatform: "RTO",
          keepInOriginal: false,
        },
      },
    };

    const spyGetTransactions = vi
      .spyOn(transactions, "getTransactions")
      .mockResolvedValue({
        // eslint-disable-next-line @typescript-eslint/naming-convention
        RFM: [],
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ETI: [],
        // eslint-disable-next-line @typescript-eslint/naming-convention
        RTO: [],
      });

    const spyGetTransactionsJaql = vi
      .spyOn(transactions, "getTransactionsWithJaql")
      .mockResolvedValue({
        // eslint-disable-next-line @typescript-eslint/naming-convention
        RFM: [],
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ETI: [],
        // eslint-disable-next-line @typescript-eslint/naming-convention
        RTO: [],
      });

    const mockRequest = {
      body: {
        serviceNumber: "1234567890",
        fromDate: {
          year: 2023,
          month: 1,
          day: 1,
        },
        toDate: {
          year: 2023,
          month: 12,
          day: 31,
        },
      },
    };

    const mockReply = {
      send: vi.fn(),
      code: vi.fn().mockReturnThis(),
    };

    // @ts-expect-error not getting the right type when mocking
    await handler(mockRequest, mockReply);

    expect(spyGetTransactions).toHaveBeenCalledWith(
      "1234567890",
      {
        fromDate: {
          year: 2023,
          month: 1,
          day: 1,
        },
        toDate: {
          year: 2023,
          month: 12,
          day: 31,
        },
      },
      getTransactionsOptions
    );
    expect(spyGetTransactionsJaql).toHaveBeenCalledWith(
      "1234567890",
      {
        fromDate: {
          year: 2023,
          month: 1,
          day: 1,
        },
        toDate: {
          year: 2023,
          month: 12,
          day: 31,
        },
      },
      getTransactionsOptions
    );
    expect(mockReply.send).toHaveBeenCalledWith({
      data: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        jaql: { RFM: [], ETI: [], RTO: [] },
        // eslint-disable-next-line @typescript-eslint/naming-convention
        sql: { RFM: [], ETI: [], RTO: [] },
      },
    });
  });

  it("should handle errors gracefully", async () => {
    const error = new Error("Test error");
    const spyGetTransactions = vi
      .spyOn(transactions, "getTransactions")
      .mockRejectedValue(error);

    const mockRequest = {
      body: {
        serviceNumber: "1234567890",
        fromDate: {
          year: 2023,
          month: 1,
          day: 1,
        },
        toDate: {
          year: 2023,
          month: 12,
          day: 31,
        },
      },
      log: {
        error: vi.fn(),
      },
    };

    const mockReply = {
      send: vi.fn(),
      code: vi.fn().mockReturnThis(),
    };

    // @ts-expect-error not getting the right type when mocking
    await handler(mockRequest, mockReply);

    expect(mockRequest.log.error).toHaveBeenCalledWith(error);
    expect(spyGetTransactions).toHaveBeenCalled();
    expect(mockReply.code).toHaveBeenCalledWith(500);
    expect(mockReply.send).toHaveBeenCalledWith({
      error: { message: "Test error" },
    });
  });
});
