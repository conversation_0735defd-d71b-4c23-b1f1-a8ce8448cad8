import { handler } from "./handler";
import * as getAuthToken from "../../../services/blusky/authentication";
import * as transactions from "../../../services/blusky/transactions";

import type { FastifyReply, FastifyRequest } from "fastify";

describe("KYC Transactions Handler", () => {
  it("should obtain and return KYC transactions through a jaql and sql", async () => {
    const spyGetAuthToken = vi
      .spyOn(getAuthToken, "getAuthToken")
      .mockResolvedValue("mocked-auth-token");

    const spyGetKycTransactionsWithJaql = vi
      .spyOn(transactions, "getKycTransactionsWithJaql")
      .mockResolvedValue([
        {
          serviceNumber: "1234567890",
          name: "<PERSON>",
          email: "<EMAIL>",
          type: "KY4",
          clientUserId: "0987654321",
          status: "STATUS_SUCCESS",
          dispCreated: "2024-08-02 02:03:10",
          dispUpdated: "2024-08-02 02:14:06",
        },
      ]);

    const spyGetKycTransactionsWithSql = vi
      .spyOn(transactions, "getKycTransactions")
      .mockResolvedValue([
        {
          serviceNumber: "1234567890",
          name: "John Doe",
          email: "<EMAIL>",
          type: "KY4",
          clientUserId: "0987654321",
          status: "STATUS_SUCCESS",
          dispCreated: "2024-08-02 02:03:10",
          dispUpdated: "2024-08-02 02:14:06",
        },
      ]);

    const mockRequest = {
      body: {
        serviceNumber: "1234567890",
        fromDate: {
          year: 2024,
          month: 1,
          day: 1,
        },
        toDate: {
          year: 2024,
          month: 12,
          day: 31,
        },
      },
      log: {
        error: vi.fn(),
      },
    } as unknown as FastifyRequest;

    const mockReply = {
      send: vi.fn(),
      code: vi.fn().mockReturnThis(),
    } as unknown as FastifyReply;

    await handler(mockRequest, mockReply);
    expect(spyGetAuthToken).toHaveBeenCalledTimes(1);
    expect(spyGetKycTransactionsWithJaql).toHaveBeenCalledWith(
      "1234567890",
      new Date("2024-01-01"),
      new Date("2024-12-31"),
      "mocked-auth-token"
    );
    expect(spyGetKycTransactionsWithSql).toHaveBeenCalledWith(
      "1234567890",
      new Date("2024-01-01"),
      new Date("2024-12-31"),
      "mocked-auth-token"
    );

    expect(mockReply.send).toHaveBeenCalledWith({
      data: {
        jaql: [
          {
            serviceNumber: "1234567890",
            name: "John Doe",
            email: "<EMAIL>",
            type: "KY4",
            clientUserId: "0987654321",
            status: "STATUS_SUCCESS",
            dispCreated: "2024-08-02 02:03:10",
            dispUpdated: "2024-08-02 02:14:06",
          },
        ],
        sql: [
          {
            serviceNumber: "1234567890",
            name: "John Doe",
            email: "<EMAIL>",
            type: "KY4",
            clientUserId: "0987654321",
            status: "STATUS_SUCCESS",
            dispCreated: "2024-08-02 02:03:10",
            dispUpdated: "2024-08-02 02:14:06",
          },
        ],
      },
    });
  });

  it("should handle errors and return a 500 status code", async () => {
    const spyGetAuthToken = vi
      .spyOn(getAuthToken, "getAuthToken")
      .mockRejectedValue(new Error("Authentication failed"));

    const mockRequest = {
      body: {
        serviceNumber: "1234567890",
        fromDate: {
          year: 2024,
          month: 1,
          day: 1,
        },
        toDate: {
          year: 2024,
          month: 12,
          day: 31,
        },
      },
      log: {
        error: vi.fn(),
      },
    } as unknown as FastifyRequest;

    const mockReply = {
      send: vi.fn(),
      code: vi.fn().mockReturnThis(),
    } as unknown as FastifyReply;

    await handler(mockRequest, mockReply);

    expect(spyGetAuthToken).toHaveBeenCalledTimes(1);
    expect(mockReply.code).toHaveBeenCalledWith(500);
    expect(mockReply.send).toHaveBeenCalledWith({
      error: { message: "Authentication failed" },
    });
  });
});
