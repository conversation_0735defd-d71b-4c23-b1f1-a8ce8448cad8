import { type DateOnly, fromDateOnly } from "@utils/date-only";

import { getAuthToken } from "../../../services/blusky/authentication";
import {
  getKycTransactions,
  getKycTransactionsWithJaql,
} from "../../../services/blusky/transactions";

import type { FastifyReply, FastifyRequest } from "fastify";

type RequestBody = {
  serviceNumber: string;
  fromDate: DateOnly;
  toDate: DateOnly;
};

export const handler = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const token = await getAuthToken();
    const { serviceNumber, fromDate, toDate } = request.body as RequestBody;

    const newFromDate = fromDateOnly(fromDate);
    const newToDate = fromDateOnly(toDate);

    const jaqlResult = await getKycTransactionsWithJaql(
      serviceNumber,
      newFromDate,
      newToDate,
      token
    );

    const sqlResult = await getKycTransactions(
      serviceNumber,
      newFromDate,
      newToDate,
      token
    );

    return await reply.send({
      data: {
        jaql: jaqlResult,
        sql: sqlResult,
      },
    });
  } catch (error) {
    request.log.error(error);
    await reply.code(500).send({
      error: { message: (error as Error).message },
    });
  }
};
