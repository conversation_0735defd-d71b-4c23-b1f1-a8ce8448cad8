import { handler } from "./handler";
import * as getAuthToken from "../../../services/blusky/authentication";
import * as transactions from "../../../services/blusky/transactions";

describe("Transactions Total Handler Tests", () => {
  it("should fetch total transaction amounts with SQL", async () => {
    const spyGetTransactionsTotalAmountJaql = vi
      .spyOn(transactions, "getTransactionsTotalAmountWithJaql")
      .mockResolvedValue({
        // eslint-disable-next-line @typescript-eslint/naming-convention
        RFM: 1000,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ETI: 2000,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        RTO: 3000,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ACH: 0,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ANR: 0,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ANX: 0,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ETO: 0,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        RTX: 0,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        IDP: 0,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ETF: 0,
      });

    const spyGetTransactionsTotalAmount = vi
      .spyOn(transactions, "getTransactionsTotalAmount")
      .mockResolvedValue({
        // eslint-disable-next-line @typescript-eslint/naming-convention
        RFM: 1,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ETI: 5,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        RTO: 9,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ACH: 0,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ANR: 0,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ANX: 0,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ETO: 0,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        RTX: 0,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        IDP: 0,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ETF: 100,
      });

    const spyGetAuthToken = vi
      .spyOn(getAuthToken, "getAuthToken")
      .mockResolvedValue("mocked-token");

    const mockRequest = {
      body: {
        serviceNumbers: ["1234567890", "0987654321"],
        fromDate: {
          year: 2023,
          month: 1,
          day: 1,
        },
        toDate: {
          year: 2023,
          month: 12,
          day: 31,
        },
      },
      log: {
        error: vi.fn(),
      },
    };

    const mockReply = {
      send: vi.fn(),
      code: vi.fn().mockReturnThis(),
      raw: {
        setTimeout: vi.fn(), // Mocking the setTimeout method
      },
    };

    // @ts-expect-error not getting the right type when mocking
    await handler(mockRequest, mockReply);

    expect(spyGetAuthToken).toHaveBeenCalledTimes(1);

    expect(spyGetTransactionsTotalAmountJaql).toHaveBeenCalledWith(
      ["1234567890", "0987654321"],
      {
        year: 2023,
        month: 1,
        day: 1,
      },
      {
        year: 2023,
        month: 12,
        day: 31,
      },
      "mocked-token"
    );

    expect(spyGetTransactionsTotalAmount).toHaveBeenCalledWith(
      ["1234567890", "0987654321"],
      {
        year: 2023,
        month: 1,
        day: 1,
      },
      {
        year: 2023,
        month: 12,
        day: 31,
      },
      "mocked-token"
    );

    expect(mockReply.send).toHaveBeenCalledWith({
      data: {
        jaql: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          RFM: 1000,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          ETI: 2000,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          RTO: 3000,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          ACH: 0,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          ANR: 0,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          ANX: 0,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          ETO: 0,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          RTX: 0,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          IDP: 0,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          ETF: 0,
        },
        sql: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          RFM: 1,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          ETI: 5,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          RTO: 9,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          ACH: 0,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          ANR: 0,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          ANX: 0,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          ETO: 0,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          RTX: 0,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          IDP: 0,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          ETF: 100,
        },
      },
    });
  });

  it("should handle errors gracefully", async () => {
    const spyGetAuthToken = vi
      .spyOn(getAuthToken, "getAuthToken")
      .mockResolvedValue("mocked-token");

    const mockError = new Error("Test error");
    const spyGetTransactionsTotalAmountJaql = vi
      .spyOn(transactions, "getTransactionsTotalAmountWithJaql")
      .mockRejectedValue(mockError);

    const mockRequest = {
      body: {
        serviceNumbers: ["1234567890", "0987654321"],
        fromDate: {
          year: 2023,
          month: 1,
          day: 1,
        },
        toDate: {
          year: 2023,
          month: 12,
          day: 31,
        },
      },
      log: {
        error: vi.fn(),
      },
    };

    const mockReply = {
      send: vi.fn(),
      code: vi.fn().mockReturnThis(),
      raw: {
        setTimeout: vi.fn(), // Mocking the setTimeout method
      },
    };

    // @ts-expect-error not getting the right type when mocking
    await handler(mockRequest, mockReply);

    expect(spyGetTransactionsTotalAmountJaql).toHaveBeenCalledWith(
      ["1234567890", "0987654321"],
      {
        year: 2023,
        month: 1,
        day: 1,
      },
      {
        year: 2023,
        month: 12,
        day: 31,
      },
      "mocked-token"
    );

    expect(spyGetAuthToken).toHaveBeenCalledTimes(1);

    expect(mockRequest.log.error).toHaveBeenCalledWith(mockError);
    expect(mockReply.code).toHaveBeenCalledWith(500);
    expect(mockReply.send).toHaveBeenCalledWith({
      error: { message: "Test error" },
    });
  });
});
