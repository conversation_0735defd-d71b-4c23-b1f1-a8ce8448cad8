import { options as getSettlementFiltersOptions } from "@lib/settlement/read/filters/options";
import { options as getSettlementOptions } from "@lib/settlement/read/options";

import { options as allFrequencyCreateOptions } from "../../lib/settlement/all/frequency/create/options";
import { options as allFrequency } from "../../lib/settlement/all/frequency/read/options";
import { options as allMonthlyCreateOptions } from "../../lib/settlement/all/monthly/create/options";
import { options as allMonthlyDeleteOptions } from "../../lib/settlement/all/monthly/delete/options";
import { options as allMonthlyHistoryOptions } from "../../lib/settlement/all/monthly/history/options";
import { options as allMonthlyReadOptions } from "../../lib/settlement/all/monthly/read/options";
import { options as allMonthlyRecalculateOptions } from "../../lib/settlement/all/monthly/recalculate/options";

import type { FastifyInstance } from "fastify";

export default async function (server: FastifyInstance) {
  server.post("/all/monthly/recalculate", allMonthlyRecalculateOptions);
  server.post("/all/monthly/read", allMonthlyReadOptions);
  server.post("/all/monthly", allMonthlyCreateOptions);
  server.get("/all/monthly/history", allMonthlyHistoryOptions);
  server.get("/all/frequency/read", allFrequency);
  server.post("/generate", allFrequencyCreateOptions);
  server.delete("/all/monthly/history", allMonthlyDeleteOptions);
  server.post("/all/frequency", allFrequencyCreateOptions);
  server.post("/", getSettlementOptions);
  server.get("/filters", getSettlementFiltersOptions);
}
