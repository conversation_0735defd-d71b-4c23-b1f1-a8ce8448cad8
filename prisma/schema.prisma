generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model AuditDb {
  auditDbId  Int       @id @default(autoincrement())
  AuditDbcol String?   @db.LongText
  timestamp  DateTime? @db.DateTime(0)
  createUser String?   @db.VarChar(45)
  createdAt  DateTime? @default(now()) @db.DateTime(0)
}

model address {
  addressId   Int           @id @default(autoincrement())
  street      String        @db.VarChar(255)
  cityName    String        @db.VarChar(255)
  stateName   String?       @db.VarChar(255)
  postalCode  String?       @db.VarChar(50)
  countryId   Int?
  createdAt   DateTime?     @default(now()) @db.Timestamp(0)
  updatedAt   DateTime?     @default(now()) @db.Timestamp(0)
  deletedAt   DateTime?     @db.Timestamp(0)
  country     country?      @relation(fields: [countryId], references: [countryId], onDelete: NoAction, onUpdate: NoAction, map: "address_ibfk_1")
  bankAccount bankAccount[]
  beneficiary beneficiary[]

  @@index([countryId], map: "countryId")
}

model audit {
  id         Int       @id @default(autoincrement())
  userId     Int?
  type       String?   @db.VarChar(255)
  preValues  String?   @db.Text
  postValues String?   @db.Text
  table      String?   @db.VarChar(255)
  tableId    String?   @db.VarChar(255)
  createdAt  DateTime? @db.DateTime(0)
  updatedAt  DateTime? @db.DateTime(0)
  comment    String?   @db.VarChar(512)
  user       user?     @relation(fields: [userId], references: [userId], onDelete: NoAction, onUpdate: NoAction, map: "audit_ibfk_1")

  @@index([userId], map: "userId")
}

model auditDbRecord {
  auditRecordId  Int      @id @default(autoincrement())
  auditId        Int
  action         String   @db.VarChar(45)
  databaseTable  String   @db.VarChar(255)
  tableKey       Int
  old_record_Xml String   @db.LongText
  new_record_Xml String   @db.LongText
  timestamp      DateTime @db.DateTime(0)
  updateUser     String   @db.VarChar(45)
  updatedDate    DateTime @default(now()) @db.DateTime(0)
  createUser     String   @db.VarChar(45)
  createdAt      DateTime @default(now()) @db.DateTime(0)
}

model balanceAlert {
  balanceAlertId        Int        @id @default(autoincrement())
  isBalanceAlertEnabled Boolean    @default(false)
  alertEmails           Json
  pseudoReserve         Int        @default(0)
  createdAt             DateTime?  @default(now()) @db.Timestamp(0)
  updatedAt             DateTime?  @default(now()) @db.Timestamp(0)
  deletedAt             DateTime?  @db.Timestamp(0)
  customer              customer[]
}

model bankAccount {
  bankAccountId     Int           @id @default(autoincrement())
  name              String        @db.VarChar(255)
  swiftCode         String        @db.VarChar(255)
  addressId         Int
  IBAN              String?       @db.VarChar(255)
  bankPaymentRailId Int
  createdAt         DateTime?     @default(now()) @db.Timestamp(0)
  updatedAt         DateTime?     @default(now()) @db.Timestamp(0)
  deletedAt         DateTime?     @db.Timestamp(0)
  address           address       @relation(fields: [addressId], references: [addressId], onDelete: NoAction, onUpdate: NoAction, map: "bankAccount_ibfk_1")
  paymentRail       paymentRail   @relation(fields: [bankPaymentRailId], references: [paymentRailId], onDelete: NoAction, onUpdate: NoAction, map: "bankAccount_ibfk_2")
  beneficiary       beneficiary[]

  @@index([addressId], map: "addressId")
  @@index([bankPaymentRailId], map: "bankPaymentRailId")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model beneficiary {
  beneficiaryId   Int            @id @default(autoincrement())
  name            String         @db.VarChar(255)
  email           String?        @db.VarChar(255)
  addressId       Int?
  reference       String?        @db.VarChar(255)
  bankAccountId   Int?
  cryptoAccountId Int?
  createdAt       DateTime?      @default(now()) @db.Timestamp(0)
  updatedAt       DateTime?      @default(now()) @db.Timestamp(0)
  deletedAt       DateTime?      @db.Timestamp(0)
  lastUpdatedBy   Int?
  address         address?       @relation(fields: [addressId], references: [addressId], onDelete: NoAction, onUpdate: NoAction, map: "beneficiary_ibfk_1")
  bankAccount     bankAccount?   @relation(fields: [bankAccountId], references: [bankAccountId], onDelete: NoAction, onUpdate: NoAction, map: "beneficiary_ibfk_2")
  cryptoAccount   cryptoAccount? @relation(fields: [cryptoAccountId], references: [cryptoAccountId], onDelete: NoAction, onUpdate: NoAction, map: "beneficiary_ibfk_3")
  user            user?          @relation(fields: [lastUpdatedBy], references: [userId], onDelete: NoAction, onUpdate: NoAction, map: "beneficiary_ibfk_4")
  wire            wire[]
  wireGroup       wireGroup?

  @@index([addressId], map: "addressId")
  @@index([bankAccountId], map: "bankAccountId")
  @@index([cryptoAccountId], map: "cryptoAccountId")
  @@index([lastUpdatedBy], map: "beneficiary_ibfk_4")
}

model country {
  countryId           Int        @id @default(autoincrement())
  countryName         String?    @db.VarChar(255)
  countryAbbreviation String?    @db.VarChar(10)
  createdAt           DateTime?  @default(now()) @db.DateTime(0)
  updatedAt           DateTime?  @default(now()) @db.DateTime(0)
  deletedAt           DateTime?  @db.DateTime(0)
  address             address[]
  customer            customer[]
  province            province[]
}

model cryptoAccount {
  cryptoAccountId     Int           @id @default(autoincrement())
  walletAddress       String        @db.VarChar(255)
  cryptoPaymentRailId Int
  createdAt           DateTime?     @default(now()) @db.Timestamp(0)
  updatedAt           DateTime?     @default(now()) @db.Timestamp(0)
  deletedAt           DateTime?     @db.Timestamp(0)
  beneficiary         beneficiary[]
  paymentRail         paymentRail   @relation(fields: [cryptoPaymentRailId], references: [paymentRailId], onDelete: NoAction, onUpdate: NoAction, map: "cryptoAccount_ibfk_1")

  @@index([cryptoPaymentRailId], map: "cryptoPaymentRailId")
}

model currency {
  currencyId     Int           @id @default(autoincrement())
  name           String        @db.VarChar(255)
  code           String        @unique(map: "code") @db.VarChar(10)
  symbol         String?       @db.VarChar(10)
  monetaryTypeId Int
  createdAt      DateTime?     @default(now()) @db.Timestamp(0)
  updatedAt      DateTime?     @default(now()) @db.Timestamp(0)
  deletedAt      DateTime?     @db.Timestamp(0)
  monetaryType   monetaryType  @relation(fields: [monetaryTypeId], references: [monetaryTypeId], onDelete: NoAction, onUpdate: NoAction, map: "currency_ibfk_1")
  paymentRail    paymentRail[]

  @@index([monetaryTypeId], map: "monetaryTypeId")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model customer {
  customerId                                                     Int                          @id @default(autoincrement())
  entityId                                                       Int
  customerName                                                   String                       @db.VarChar(50)
  serviceNumber                                                  String                       @unique(map: "sk_serviceNumber_Unique") @db.VarChar(20)
  enabled                                                        Boolean                      @default(true)
  createdAt                                                      DateTime?                    @default(now()) @db.DateTime(0)
  updatedAt                                                      DateTime?                    @default(now()) @db.DateTime(0)
  deletedAt                                                      DateTime?                    @db.DateTime(0)
  customerSettlementTypeId                                       Int?
  customerTradingName                                            String?                      @db.VarChar(50)
  multipleServiceNoTierSet                                       Boolean?
  multipleServiceNoTierSetKyc                                    Boolean?
  firstTransactionDate                                           DateTime?                    @db.Date
  minimumMonthlyAmountKyc                                        Decimal?                     @db.Decimal(12, 2)
  countryId                                                      Int?
  provinceId                                                     Int?
  wireGroupId                                                    Int?
  emailConfigurationId                                           Int?
  balanceAlertId                                                 Int?
  country                                                        country?                     @relation(fields: [countryId], references: [countryId], onDelete: Restrict, map: "fk_cusomer_country")
  balanceAlert                                                   balanceAlert?                @relation(fields: [balanceAlertId], references: [balanceAlertId], onDelete: NoAction, onUpdate: NoAction, map: "fk_customer_balanceAlert")
  customerSettlementType                                         customerSettlementType?      @relation(fields: [customerSettlementTypeId], references: [customerSettlementTypeId], onDelete: NoAction, onUpdate: NoAction, map: "fk_customer_customerSettlementType")
  emailConfiguration                                             emailConfiguration?          @relation(fields: [emailConfigurationId], references: [emailConfigurationId], onDelete: NoAction, onUpdate: NoAction, map: "fk_customer_emailConfigurationId")
  entity                                                         entity                       @relation(fields: [entityId], references: [entityId], onDelete: NoAction, onUpdate: NoAction, map: "fk_customer_entity")
  province                                                       province?                    @relation(fields: [provinceId], references: [provinceId], onDelete: NoAction, onUpdate: NoAction, map: "fk_customer_province")
  wireGroup                                                      wireGroup?                   @relation(fields: [wireGroupId], references: [wireGroupId], onDelete: NoAction, onUpdate: NoAction, map: "fk_customer_wireGroupId")
  customerCustomerType                                           customerCustomerType[]
  customerMonthlyTransaction                                     customerMonthlyTransaction[]
  customerReserve                                                customerReserve[]
  customerSettlements                                            customerSettlements[]
  customerWireInOuts                                             customerWireInOuts[]
  merchantPlatform_merchantPlatform_agentCustomerIdTocustomer    merchantPlatform[]           @relation("merchantPlatform_agentCustomerIdTocustomer")
  merchantPlatform_merchantPlatform_subAgentCustomerIdTocustomer merchantPlatform[]           @relation("merchantPlatform_subAgentCustomerIdTocustomer")
  merchantPlatform_merchantPlatform_clientCustomerIdTocustomer   merchantPlatform[]           @relation("merchantPlatform_clientCustomerIdTocustomer")
  wire                                                           wire[]

  @@index([countryId], map: "fk_cusomer_country_idx")
  @@index([balanceAlertId], map: "fk_customer_balanceAlert")
  @@index([customerSettlementTypeId], map: "fk_customer_customerSettlementType")
  @@index([emailConfigurationId], map: "fk_customer_emailConfigurationId")
  @@index([entityId], map: "fk_customer_entity")
  @@index([provinceId], map: "fk_customer_province")
  @@index([wireGroupId], map: "fk_customer_wireGroupId")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model customerCustomerType {
  customerCustomerTypeId            Int                                 @id @default(autoincrement())
  customerId                        Int
  customerTypeId                    Int
  createdAt                         DateTime?                           @default(now()) @db.DateTime(0)
  updatedAt                         DateTime?                           @default(now()) @db.DateTime(0)
  deletedAt                         DateTime?                           @db.DateTime(0)
  statementFolderLocation           String?                             @db.VarChar(512)
  statementFrequencyId              Int
  includeIDPTierSet                 Int?                                @db.TinyInt
  includeRTOTierSet                 Int?                                @db.TinyInt
  includeACHTierSet                 Boolean                             @default(false)
  customer                          customer                            @relation(fields: [customerId], references: [customerId], onDelete: NoAction, onUpdate: NoAction, map: "fk_customer")
  customerType                      customerType                        @relation(fields: [customerTypeId], references: [customerTypeId], onDelete: NoAction, onUpdate: NoAction, map: "fk_customer_type")
  statementFrequency                statementFrequency                  @relation(fields: [statementFrequencyId], references: [statementFrequencyId], onDelete: NoAction, onUpdate: NoAction, map: "fk_statement_frequency")
  customerCustomerTypeServiceNumber customerCustomerTypeServiceNumber[]
  customerSettlements               customerSettlements[]
  monthlyCustomerSettlement         monthlyCustomerSettlement[]
  volumeCombination                 volumeCombination[]

  @@unique([customerId, customerTypeId], map: "sk_customerId_customerTypeId_Unique")
  @@index([customerTypeId], map: "fk_customerType__CustomerCustomerType")
  @@index([statementFrequencyId], map: "fk_statement_frequency")
}

model customerCustomerTypeServiceNumber {
  customerCustomerTypeServiceNumberId Int                   @id @default(autoincrement())
  customerCustomerTypeId              Int?
  serviceNumber                       String?               @db.VarChar(20)
  fromDate                            DateTime?             @db.Date
  toDate                              DateTime?             @db.Date
  createdAt                           DateTime?             @default(now()) @db.DateTime(0)
  updatedAt                           DateTime?             @default(now()) @db.DateTime(0)
  deletedAt                           DateTime?             @db.DateTime(0)
  customerCustomerType                customerCustomerType? @relation(fields: [customerCustomerTypeId], references: [customerCustomerTypeId], onDelete: NoAction, onUpdate: NoAction, map: "customerCustomerTypeServiceNumber_ibfk_1")

  @@index([customerCustomerTypeId], map: "customerCustomerTypeId")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model customerMonthlyTransaction {
  customerMonthlyTransactionId     Int                               @id @default(autoincrement())
  customerId                       Int
  platformId                       Int
  month                            Int
  year                             Int
  status                           customerMonthlyTransaction_status
  transactionFeeTotal              Float?                            @default(0)
  salesFeeTotal                    Float?                            @default(0)
  refundfeeTotal                   Float?                            @default(0)
  rejected1FeeTotal                Float?                            @default(0)
  meta                             Json?
  jobId                            Int?
  createdAt                        DateTime?                         @default(now()) @db.Timestamp(0)
  updatedAt                        DateTime?                         @default(now()) @db.Timestamp(0)
  deletedAt                        DateTime?                         @db.Timestamp(0)
  customer                         customer                          @relation(fields: [customerId], references: [customerId], onDelete: NoAction, onUpdate: NoAction, map: "customerMonthlyTransaction_ibfk_1")
  platform                         platform                          @relation(fields: [platformId], references: [platformId], onDelete: NoAction, onUpdate: NoAction, map: "customerMonthlyTransaction_ibfk_2")
  monthlyTransactionCalculationJob monthlyTransactionCalculationJob? @relation(fields: [jobId], references: [monthlyTransactionCalculationJobId], onDelete: NoAction, onUpdate: NoAction, map: "customerMonthlyTransaction_ibfk_3")

  @@index([customerId], map: "customerId")
  @@index([jobId], map: "jobId")
  @@index([platformId], map: "platformId")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model customerReserve {
  customerReserveId Int       @unique(map: "pk_customerReserve") @default(autoincrement())
  customerId        Int
  transactionDate   DateTime  @db.Date
  transactionAmount Decimal?  @db.Decimal(12, 2)
  transactionType   String    @db.VarChar(2)
  createdAt         DateTime  @default(now()) @db.DateTime(0)
  deletedAt         DateTime? @db.DateTime(0)
  deletedReason     String?   @db.Text
  updatedAt         DateTime? @default(now()) @db.DateTime(0)
  updatedReason     String?   @db.Text
  comment           String?   @db.Text
  endBalance        Decimal?  @db.Decimal(12, 2)
  customer          customer  @relation(fields: [customerId], references: [customerId], onDelete: NoAction, onUpdate: NoAction, map: "customercustomerReserve_ibfk_1")

  @@index([customerId], map: "fk_customer_customerReserve")
}

model customerSettlementAdjustments {
  customerSettlementAdjustmentId Int                  @id @default(autoincrement())
  customerSettlementsId          Int?
  label                          String?              @db.VarChar(255)
  amount                         Decimal?             @default(0.00) @db.Decimal(12, 2)
  displayCommentExcel            Boolean?             @default(false)
  comment                        String?              @db.VarChar(255)
  createdAt                      DateTime?            @default(now()) @db.DateTime(0)
  updatedAt                      DateTime?            @default(now()) @db.DateTime(0)
  deletedAt                      DateTime?            @db.DateTime(0)
  customerSettlements            customerSettlements? @relation(fields: [customerSettlementsId], references: [customerSettlementsId], onDelete: NoAction, onUpdate: NoAction, map: "customerSettlementAdjustments_ibfk_1")

  @@index([customerSettlementsId], map: "customerSettlementsId")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model customerSettlementGenerationSchedule {
  customerSettlementGenerationScheduleId Int       @id @default(autoincrement())
  serviceNumber                          String    @db.VarChar(20)
  fromDate                               DateTime? @db.Date
  toDate                                 DateTime? @db.Date
  generationDate                         DateTime? @db.Date
  startDate                              DateTime? @db.DateTime(0)
  completionDate                         DateTime? @db.DateTime(0)
  generationType                         String?   @db.VarChar(45)
  generationStatus                       String?   @db.VarChar(45)
  errorMessage                           String?   @db.VarChar(255)
  parentScheduleId                       Int?
  createdAt                              DateTime? @default(now()) @db.DateTime(0)
  updatedAt                              DateTime? @default(now()) @db.DateTime(0)
  deletedAt                              DateTime? @db.DateTime(0)
  customerCustomerTypeId                 Int?
  fromUI                                 Int?      @db.TinyInt

  @@index([customerSettlementGenerationScheduleId], map: "pk_customerSettlementGenerationSchedule")
}

model customerSettlementJob {
  customerSettlementJobId Int                          @id @default(autoincrement())
  fromDate                DateTime                     @db.Date
  toDate                  DateTime                     @db.Date
  status                  customerSettlementJob_status @default(PROGRESS)
  frequencyId             Int?
  userId                  Int?
  meta                    Json?
  createdAt               DateTime?                    @default(now()) @db.Timestamp(0)
  updatedAt               DateTime?                    @default(now()) @db.Timestamp(0)
  deletedAt               DateTime?                    @db.Timestamp(0)
  user                    user?                        @relation(fields: [userId], references: [userId], onDelete: NoAction, onUpdate: NoAction, map: "customerSettlementJob_ibfk_1")
  statementFrequency      statementFrequency?          @relation(fields: [frequencyId], references: [statementFrequencyId], onDelete: NoAction, onUpdate: NoAction, map: "customerSettlementJob_ibfk_2")

  @@index([frequencyId], map: "frequencyId")
  @@index([userId], map: "userId")
}

model customerSettlementKyc {
  customerSettlementsKycId Int                 @id @default(autoincrement())
  customerSettlementsId    Int
  kycTypeId                Int
  tierItemId               Int
  transactionCount         Int?
  totalTransactionAmount   Decimal?            @db.Decimal(12, 2)
  createdAt                DateTime?           @default(now()) @db.DateTime(0)
  updatedAt                DateTime?           @default(now()) @db.DateTime(0)
  deletedAt                DateTime?           @db.DateTime(0)
  customerSettlements      customerSettlements @relation(fields: [customerSettlementsId], references: [customerSettlementsId], onDelete: NoAction, map: "FK_customerSettlementsKyc_customerSettlements")
  kycType                  kycType             @relation(fields: [kycTypeId], references: [kycTypeId], map: "FK_customerSettlementsKyc_kycType")

  @@index([customerSettlementsId], map: "FK_customerSettlementsKyc_customerSettlements_idx")
  @@index([kycTypeId], map: "FK_customerSettlementsKyc_kycType_idx")
}

model customerSettlementKycTax {
  customerSettlementKycTaxId Int                 @id @default(autoincrement())
  taxTypeId                  Int
  taxAmount                  Decimal             @default(0.00) @db.Decimal(12, 2)
  customerSettlementsId      Int
  createdAt                  DateTime            @default(now()) @db.DateTime(0)
  updatedAt                  DateTime            @default(now()) @db.DateTime(0)
  deletedAt                  DateTime?           @db.DateTime(0)
  customerSettlements        customerSettlements @relation(fields: [customerSettlementsId], references: [customerSettlementsId], onDelete: NoAction, onUpdate: NoAction, map: "customerSettlementKycTax_FK")
  taxTypes                   taxTypes            @relation(fields: [taxTypeId], references: [taxTypeId], onDelete: NoAction, onUpdate: NoAction, map: "customerSettlementKycTax_FK_1")

  @@index([customerSettlementsId], map: "customerSettlementKycTax_FK")
  @@index([taxTypeId], map: "customerSettlementKycTax_FK_1")
}

model customerSettlementType {
  customerSettlementTypeId Int        @id @default(autoincrement())
  description              String?    @db.VarChar(255)
  createdAt                DateTime?  @default(now()) @db.Timestamp(0)
  updatedAt                DateTime?  @default(now()) @db.DateTime(0)
  deletedAt                DateTime?  @db.DateTime(0)
  customer                 customer[]
}

model customerSettlements {
  customerSettlementsId         Int                             @id @default(autoincrement())
  customerId                    Int
  platformId                    Int
  fromDate                      DateTime                        @db.Date
  toDate                        DateTime                        @db.Date
  approvedBy                    Int?
  transactionCount              Int?                            @default(0)
  totalTransactionAmount        Decimal?                        @default(0.00) @db.Decimal(12, 2)
  refundCount                   Int?                            @default(0)
  totalRefundAmount             Decimal?                        @default(0.00) @db.Decimal(12, 2)
  gatewayFee                    Decimal?                        @default(0.00) @db.Decimal(12, 2)
  transactionFee                Decimal?                        @default(0.00) @db.Decimal(12, 2)
  salesFee                      Decimal?                        @default(0.00) @db.Decimal(12, 2)
  refundFee                     Decimal?                        @default(0.00) @db.Decimal(12, 2)
  totalFailedAmount             Decimal?                        @default(0.00) @db.Decimal(12, 2)
  status                        String?                         @db.VarChar(45)
  tierItemId                    Int?
  endBalance                    Decimal?                        @default(0.00) @db.Decimal(12, 2)
  createdAt                     DateTime?                       @default(now()) @db.DateTime(0)
  updatedAt                     DateTime?                       @default(now()) @db.DateTime(0)
  deletedAt                     DateTime?                       @db.DateTime(0)
  regenerationCount             Int?                            @default(0)
  emailSentAt                   DateTime?                       @db.Date
  customerCustomerTypeId        Int?
  merchantPlatformId            Int?
  integratorFee                 Decimal?                        @default(0.00) @db.Decimal(12, 2)
  agentFee                      Decimal?                        @default(0.00) @db.Decimal(12, 2)
  subAgentFee                   Decimal?                        @default(0.00) @db.Decimal(12, 2)
  total2FaRejectAmount          Decimal?                        @db.Decimal(10, 0)
  total2FaRejectCount           Int?
  txnAmountRTO_R                Float                           @default(0)
  txnCountETI_R1                Int                             @default(0)
  minimumFeeTotal               Decimal?                        @default(0.000) @db.Decimal(16, 3)
  minimumFeeCount               Int?                            @default(0)
  totalMinimumAmount            Decimal?                        @default(0.000) @db.Decimal(16, 3)
  partialReturnAmountRTO        Float                           @default(0)
  partialReturnCountRTO         Int                             @default(0)
  meta                          Json?
  customerSettlementAdjustments customerSettlementAdjustments[]
  customerSettlementKyc         customerSettlementKyc[]
  customerSettlementKycTax      customerSettlementKycTax[]
  tierItem                      tierItem?                       @relation(fields: [tierItemId], references: [tierItemId], onDelete: NoAction, onUpdate: NoAction, map: "customerSettlements_FK")
  customer                      customer                        @relation(fields: [customerId], references: [customerId], onDelete: NoAction, onUpdate: NoAction, map: "customer_settlements_FK")
  customerCustomerType          customerCustomerType?           @relation(fields: [customerCustomerTypeId], references: [customerCustomerTypeId], onDelete: NoAction, onUpdate: NoAction, map: "fk_customer_customer_Type")
  platform                      platform                        @relation(fields: [platformId], references: [platformId], onDelete: NoAction, onUpdate: NoAction, map: "fk_platform")
  wire                          wire[]

  @@unique([customerId, platformId, fromDate, toDate], map: "customer_settlements_UN")
  @@index([tierItemId], map: "customerSettlements_FK")
  @@index([customerCustomerTypeId], map: "fk_customer_customer_Type")
  @@index([platformId], map: "fk_platform")
}

model customerType {
  customerTypeId       Int                    @id @default(autoincrement())
  customerTypeName     String                 @db.VarChar(40)
  createdAt            DateTime?              @default(now()) @db.DateTime(0)
  updatedAt            DateTime?              @default(now()) @db.DateTime(0)
  deletedAt            DateTime?              @db.DateTime(0)
  customerCustomerType customerCustomerType[]
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model customerWireInOuts {
  customerWireInPayOutId Int       @unique(map: "pk_customerPayInPayOuts") @default(autoincrement())
  customerId             Int
  transactionDate        DateTime  @db.Date
  transactionAmount      Decimal?  @db.Decimal(12, 2)
  transactionType        String    @db.VarChar(2)
  createdAt              DateTime  @default(now()) @db.DateTime(0)
  deletedAt              DateTime? @db.DateTime(0)
  deletedReason          String?   @db.Text
  updatedAt              DateTime? @default(now()) @db.DateTime(0)
  updatedReason          String?   @db.Text
  comment                String?   @db.Text
  customer               customer  @relation(fields: [customerId], references: [customerId], onDelete: NoAction, onUpdate: NoAction, map: "customerWireInOuts_ibfk_1")
  email                  email[]
  wire                   wire[]

  @@index([customerId], map: "fk_customer_customerPayInPayOuts")
}

model debezium_signals {
  id   String  @id @db.VarChar(60)
  type String  @db.VarChar(32)
  data String? @db.VarChar(2048)
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model email {
  emailId            Int                 @id @default(autoincrement())
  wireId             Int?
  transferInId       Int?
  sender             String?             @db.VarChar(255)
  toMeta             Json?
  ccMeta             Json?
  subject            String              @db.Text
  body               String?             @db.MediumText
  attachments        Json?
  comment            String?             @db.Text
  scheduledSendDate  DateTime            @db.Date
  sentAt             DateTime?           @db.Timestamp(0)
  sentById           Int?
  createdAt          DateTime?           @default(now()) @db.Timestamp(0)
  updatedAt          DateTime?           @default(now()) @db.Timestamp(0)
  deletedAt          DateTime?           @db.Timestamp(0)
  sendErrorMessage   String?             @db.VarChar(255)
  isEndBalance       Boolean             @default(true)
  wire               wire?               @relation(fields: [wireId], references: [wireId], onDelete: NoAction, onUpdate: NoAction, map: "email_ibfk_1")
  customerWireInOuts customerWireInOuts? @relation(fields: [transferInId], references: [customerWireInPayOutId], onDelete: NoAction, onUpdate: NoAction, map: "email_ibfk_2")
  user               user?               @relation(fields: [sentById], references: [userId], onDelete: NoAction, onUpdate: NoAction, map: "email_ibfk_3")

  @@index([sentById], map: "idx_sentById")
  @@index([transferInId], map: "transferInId")
  @@index([wireId], map: "wireId")
}

model emailConfiguration {
  emailConfigurationId Int        @id @default(autoincrement())
  toMeta               Json
  ccMeta               Json?
  isBluskyEnabled      Boolean?   @default(false)
  isSftpEnabled        Boolean?   @default(false)
  isEmailEnabled       Boolean?   @default(true)
  createdAt            DateTime?  @default(now()) @db.Timestamp(0)
  updatedAt            DateTime?  @default(now()) @db.Timestamp(0)
  deletedAt            DateTime?  @db.Timestamp(0)
  customer             customer[]
}

model entity {
  entityId   Int        @id @default(autoincrement())
  entityName String?    @db.VarChar(50)
  createdAt  DateTime?  @default(now()) @db.DateTime(0)
  updatedAt  DateTime?  @default(now()) @db.DateTime(0)
  deletedAt  DateTime?  @db.DateTime(0)
  logoName   String?    @db.VarChar(100)
  domainName String?    @db.VarChar(50)
  customer   customer[]
}

model kycType {
  kycTypeId             Int                     @id @default(autoincrement())
  kycName               String?                 @db.VarChar(45)
  createdAt             DateTime?               @default(now()) @db.DateTime(0)
  updatedAt             DateTime?               @default(now()) @db.DateTime(0)
  deletedAt             DateTime?               @db.DateTime(0)
  kycDescription        String?                 @db.VarChar(255)
  displaySequence       Int?                    @db.TinyInt
  customerSettlementKyc customerSettlementKyc[]
  merchantPlatformKyc   merchantPlatformKyc[]
}

model merchantPlatform {
  merchantPlatformId                                        Int                   @id @default(autoincrement())
  clientCustomerId                                          Int
  clientPlatformId                                          Int
  clientDelayMonths                                         Int                   @default(0)
  clientTierSetId                                           Int
  clientTransactionFee                                      Decimal               @default(0.00) @db.Decimal(6, 2)
  clientGatewayFee                                          Decimal               @default(0.0000) @db.Decimal(5, 4)
  clientRefundFee                                           Decimal               @default(0.00) @db.Decimal(6, 2)
  agentCustomerId                                           Int?
  agentCumulativeOrSplit                                    String?               @db.Char(1)
  agentTierSetId                                            Int?
  agentSaleTierSetId                                        Int?
  agentTransactionFee                                       Decimal?              @db.Decimal(6, 2)
  agentGatewayFee                                           Decimal?              @db.Decimal(5, 4)
  subAgentCustomerId                                        Int?
  subAgentCumulativeOrSplit                                 String?               @db.Char(1)
  subAgentTierSetId                                         Int?
  subAgentSaleTierSetId                                     Int?
  subAgentTransactionFee                                    Decimal?              @db.Decimal(6, 2)
  subAgentGatewayFee                                        Decimal?              @db.Decimal(5, 4)
  integratorCustomerId                                      Int?
  integratorCumulativeOrSplit                               String?               @db.Char(1)
  integratorTierSetId                                       Int?
  integratorSaleTierSetId                                   Int?
  integratorTransactionFee                                  Decimal?              @db.Decimal(6, 2)
  integratorGatewayFee                                      Decimal?              @db.Decimal(5, 4)
  clientDelayMonthsTierItemId                               Int?
  createdAt                                                 DateTime?             @default(now()) @db.DateTime(0)
  updatedAt                                                 DateTime?             @default(now()) @db.DateTime(0)
  deletedAt                                                 DateTime?             @db.DateTime(0)
  fromDate                                                  DateTime?             @db.Date
  toDate                                                    DateTime?             @db.Date
  rejectOneTierSetId                                        Int?
  hasMinimum                                                Boolean?              @default(false)
  minimumCharge                                             Decimal?              @default(0.000) @db.Decimal(10, 3)
  minimumThreshold                                          Decimal?              @default(0.00) @db.Decimal(6, 2)
  customer_merchantPlatform_agentCustomerIdTocustomer       customer?             @relation("merchantPlatform_agentCustomerIdTocustomer", fields: [agentCustomerId], references: [customerId], onDelete: NoAction, onUpdate: NoAction, map: "fk_Merchant_Platform__CustomerId_Agent")
  customer_merchantPlatform_subAgentCustomerIdTocustomer    customer?             @relation("merchantPlatform_subAgentCustomerIdTocustomer", fields: [subAgentCustomerId], references: [customerId], onDelete: NoAction, onUpdate: NoAction, map: "fk_Merchant_Platform__CustomerId_AgentOfAgent")
  platform                                                  platform              @relation(fields: [clientPlatformId], references: [platformId], onDelete: NoAction, onUpdate: NoAction, map: "fk_Merchant_Platform__Platform_Client")
  tierSet_merchantPlatform_agentTierSetIdTotierSet          tierSet?              @relation("merchantPlatform_agentTierSetIdTotierSet", fields: [agentTierSetId], references: [tierSetId], onDelete: NoAction, onUpdate: NoAction, map: "fk_Merchant_Platform__TierSet_Agent")
  tierSet_merchantPlatform_subAgentTierSetIdTotierSet       tierSet?              @relation("merchantPlatform_subAgentTierSetIdTotierSet", fields: [subAgentTierSetId], references: [tierSetId], onDelete: NoAction, onUpdate: NoAction, map: "fk_Merchant_Platform__TierSet_AgentOfAgent")
  tierSet_merchantPlatform_subAgentSaleTierSetIdTotierSet   tierSet?              @relation("merchantPlatform_subAgentSaleTierSetIdTotierSet", fields: [subAgentSaleTierSetId], references: [tierSetId], onDelete: NoAction, onUpdate: NoAction, map: "fk_Merchant_Platform__TierSet_AgentOfAgent_Sale")
  tierSet_merchantPlatform_agentSaleTierSetIdTotierSet      tierSet?              @relation("merchantPlatform_agentSaleTierSetIdTotierSet", fields: [agentSaleTierSetId], references: [tierSetId], onDelete: NoAction, onUpdate: NoAction, map: "fk_Merchant_Platform__TierSet_Agent_Sale")
  tierSet_merchantPlatform_clientTierSetIdTotierSet         tierSet               @relation("merchantPlatform_clientTierSetIdTotierSet", fields: [clientTierSetId], references: [tierSetId], onDelete: NoAction, onUpdate: NoAction, map: "fk_Merchant_Platform__TierSet_Client")
  customer_merchantPlatform_clientCustomerIdTocustomer      customer              @relation("merchantPlatform_clientCustomerIdTocustomer", fields: [clientCustomerId], references: [customerId], onDelete: NoAction, onUpdate: NoAction, map: "fk_Merchnat_Platform__CustomerId_Client")
  tierSet_merchantPlatform_rejectOneTierSetIdTotierSet      tierSet?              @relation("merchantPlatform_rejectOneTierSetIdTotierSet", fields: [rejectOneTierSetId], references: [tierSetId], onDelete: NoAction, onUpdate: NoAction, map: "fk_rejectOneTierSetId")
  tierSet_merchantPlatform_integratorTierSetIdTotierSet     tierSet?              @relation("merchantPlatform_integratorTierSetIdTotierSet", fields: [integratorTierSetId], references: [tierSetId], onDelete: NoAction, onUpdate: NoAction, map: "merchantPlatform_ibfk_1")
  tierSet_merchantPlatform_integratorSaleTierSetIdTotierSet tierSet?              @relation("merchantPlatform_integratorSaleTierSetIdTotierSet", fields: [integratorSaleTierSetId], references: [tierSetId], onDelete: NoAction, onUpdate: NoAction, map: "merchantPlatform_ibfk_2")
  tierItem                                                  tierItem?             @relation(fields: [clientDelayMonthsTierItemId], references: [tierItemId], onDelete: NoAction, onUpdate: NoAction, map: "merchant_platform_FK")
  merchantPlatformKyc                                       merchantPlatformKyc[]

  @@index([agentCustomerId], map: "fk_Merchant_Platform__CustomerId_Agent")
  @@index([subAgentCustomerId], map: "fk_Merchant_Platform__CustomerId_AgentOfAgent")
  @@index([clientPlatformId], map: "fk_Merchant_Platform__Platform_Client")
  @@index([agentTierSetId], map: "fk_Merchant_Platform__TierSet_Agent")
  @@index([subAgentTierSetId], map: "fk_Merchant_Platform__TierSet_AgentOfAgent")
  @@index([subAgentSaleTierSetId], map: "fk_Merchant_Platform__TierSet_AgentOfAgent_Sale")
  @@index([agentSaleTierSetId], map: "fk_Merchant_Platform__TierSet_Agent_Sale")
  @@index([clientTierSetId], map: "fk_Merchant_Platform__TierSet_Client")
  @@index([clientCustomerId], map: "fk_Merchnat_Platform__CustomerId_Client_idx")
  @@index([rejectOneTierSetId], map: "fk_rejectOneTierSetId")
  @@index([integratorTierSetId], map: "merchantPlatform_ibfk_1")
  @@index([integratorSaleTierSetId], map: "merchantPlatform_ibfk_2")
  @@index([clientDelayMonthsTierItemId], map: "merchant_platform_FK")
}

model merchantPlatformKyc {
  merchantPlatformKycId       Int              @id @default(autoincrement())
  merchantPlatformId          Int
  kycTypeId                   Int
  tierSetId                   Int
  createdAt                   DateTime?        @default(now()) @db.DateTime(0)
  updatedAt                   DateTime?        @default(now()) @db.DateTime(0)
  deletedAt                   DateTime?        @db.DateTime(0)
  fromDate                    DateTime         @db.Date
  toDate                      DateTime?        @db.Date
  clientDelayMonths           Int?
  clientDelayMonthsTierItemId Int?
  firstTransactionDate        DateTime?        @db.Date
  kycType                     kycType          @relation(fields: [kycTypeId], references: [kycTypeId], map: "fk_merchantPlatformKyc_kycType")
  merchantPlatform            merchantPlatform @relation(fields: [merchantPlatformId], references: [merchantPlatformId], onDelete: NoAction, map: "fk_merchantPlatformKyc_merchantPlatform")
  tierSet                     tierSet          @relation(fields: [tierSetId], references: [tierSetId], onDelete: NoAction, map: "fk_merchantPlatformKyc_tierSet")

  @@index([kycTypeId], map: "fk_merchantPlatformKyc_kycType_idx")
  @@index([merchantPlatformId], map: "fk_merchantPlatformKyc_merchantPlatform_idx")
  @@index([tierSetId], map: "fk_merchantPlatformKyc_tierSet_idx")
}

model monetaryType {
  monetaryTypeId Int        @id @default(autoincrement())
  name           String     @unique(map: "name") @db.VarChar(255)
  createdAt      DateTime?  @default(now()) @db.Timestamp(0)
  updatedAt      DateTime?  @default(now()) @db.Timestamp(0)
  deletedAt      DateTime?  @db.Timestamp(0)
  currency       currency[]
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model monthlyAllSettlementJob {
  monthlyAllSettlementJobId Int                            @id @default(autoincrement())
  status                    monthlyAllSettlementJob_status @default(PROGRESS)
  month                     Int
  year                      Int
  generatedById             Int
  meta                      Json?
  createdAt                 DateTime?                      @default(now()) @db.Timestamp(0)
  updatedAt                 DateTime?                      @default(now()) @db.Timestamp(0)
  deletedAt                 DateTime?                      @db.Timestamp(0)
  uniquenessKey             String?                        @db.VarChar(255)
  user                      user                           @relation(fields: [generatedById], references: [userId], onDelete: NoAction, onUpdate: NoAction, map: "monthlyAllSettlementJob_ibfk_1")
  monthlyCustomerSettlement monthlyCustomerSettlement[]

  @@unique([uniquenessKey, deletedAt], map: "unique_success_constraint")
  @@index([generatedById], map: "generatedById")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model monthlyCustomerSettlement {
  monthlyCustomerSettlementId Int                              @id @default(autoincrement())
  customerCustomerTypeId      Int
  month                       Int
  year                        Int
  status                      monthlyCustomerSettlement_status
  adjustmentTotal             Decimal?                         @default(0.0000) @db.Decimal(16, 4)
  regeneratedById             Int?
  regeneratedAt               DateTime?                        @db.Timestamp(0)
  jobId                       Int?
  meta                        Json?
  createdAt                   DateTime?                        @default(now()) @db.Timestamp(0)
  updatedAt                   DateTime?                        @default(now()) @db.Timestamp(0)
  deletedAt                   DateTime?                        @db.Timestamp(0)
  uniquenessKey               String?                          @db.VarChar(255)
  customerCustomerType        customerCustomerType             @relation(fields: [customerCustomerTypeId], references: [customerCustomerTypeId], onDelete: NoAction, onUpdate: NoAction, map: "monthlyCustomerSettlement_ibfk_1")
  user                        user?                            @relation(fields: [regeneratedById], references: [userId], onDelete: NoAction, onUpdate: NoAction, map: "monthlyCustomerSettlement_ibfk_2")
  monthlyAllSettlementJob     monthlyAllSettlementJob?         @relation(fields: [jobId], references: [monthlyAllSettlementJobId], onDelete: NoAction, onUpdate: NoAction, map: "monthlyCustomerSettlement_ibfk_3")
  monthlyPlatformSettlement   monthlyPlatformSettlement[]

  @@unique([uniquenessKey, deletedAt], map: "unique_success_constraint")
  @@index([customerCustomerTypeId], map: "customerCustomerTypeId")
  @@index([jobId], map: "jobId")
  @@index([regeneratedById], map: "regeneratedById")
}

model monthlyPlatformSettlement {
  monthlyPlatformSettlementId Int                       @id @default(autoincrement())
  monthlyCustomerSettlementId Int
  platformId                  Int
  transactionFeeTotal         Decimal?                  @default(0.0000) @db.Decimal(16, 4)
  salesFeeTotal               Decimal?                  @default(0.0000) @db.Decimal(16, 4)
  gatewayFeeTotal             Decimal?                  @default(0.0000) @db.Decimal(16, 4)
  refundFeeTotal              Decimal?                  @default(0.0000) @db.Decimal(16, 4)
  rejected1FeeTotal           Decimal?                  @default(0.0000) @db.Decimal(16, 4)
  minimumFeeTotal             Decimal?                  @default(0.0000) @db.Decimal(16, 4)
  partialReturnFeeTotal       Decimal                   @default(0.0000) @db.Decimal(14, 4)
  commissionTotal             Decimal                   @default(0.0000) @db.Decimal(14, 4)
  adjustmentTotal             Decimal?                  @default(0.0000) @db.Decimal(16, 4)
  isContractChanged           Boolean?                  @default(false)
  trueUpMeta                  Json?
  settlementMeta              Json?
  createdAt                   DateTime?                 @default(now()) @db.Timestamp(0)
  updatedAt                   DateTime?                 @default(now()) @db.Timestamp(0)
  deletedAt                   DateTime?                 @db.Timestamp(0)
  platform                    platform                  @relation(fields: [platformId], references: [platformId], onDelete: NoAction, onUpdate: NoAction, map: "monthlyPlatformSettlement_ibfk_1")
  monthlyCustomerSettlement   monthlyCustomerSettlement @relation(fields: [monthlyCustomerSettlementId], references: [monthlyCustomerSettlementId], onDelete: NoAction, onUpdate: NoAction, map: "monthlyPlatformSettlement_ibfk_2")

  @@unique([monthlyCustomerSettlementId, platformId, deletedAt], map: "unique_platform_settlement")
  @@index([platformId], map: "platformId")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model monthlyTransactionCalculationJob {
  monthlyTransactionCalculationJobId Int                                     @id @default(autoincrement())
  status                             monthlyTransactionCalculationJob_status
  month                              Int
  year                               Int
  meta                               Json?
  createdAt                          DateTime?                               @default(now()) @db.Timestamp(0)
  updatedAt                          DateTime?                               @default(now()) @db.Timestamp(0)
  deletedAt                          DateTime?                               @db.Timestamp(0)
  customerMonthlyTransaction         customerMonthlyTransaction[]
}

model nonMerchantFees {
  nonMerchantFeesId Int       @id @default(autoincrement())
  customerId        Int
  platformId        Int
  fromDate          DateTime  @db.Date
  toDate            DateTime  @db.Date
  fees              Decimal   @default(0.00) @db.Decimal(10, 2)
  createdAt         DateTime  @db.DateTime(0)
  updatedAt         DateTime  @db.DateTime(0)
  deletedAt         DateTime? @db.DateTime(0)
  platform          platform  @relation(fields: [platformId], references: [platformId], onDelete: NoAction, onUpdate: NoAction, map: "nonMerchantFees_FK")

  @@index([platformId], map: "nonMerchantFees_FK")
}

model paymentRail {
  paymentRailId    Int             @id @default(autoincrement())
  paymentServiceId Int
  currencyId       Int
  disabled         Boolean?        @default(false)
  createdAt        DateTime?       @default(now()) @db.Timestamp(0)
  updatedAt        DateTime?       @default(now()) @db.Timestamp(0)
  deletedAt        DateTime?       @db.Timestamp(0)
  bankAccount      bankAccount[]
  cryptoAccount    cryptoAccount[]
  paymentService   paymentService  @relation(fields: [paymentServiceId], references: [paymentServiceId], onDelete: NoAction, onUpdate: NoAction, map: "paymentRail_ibfk_1")
  currency         currency        @relation(fields: [currencyId], references: [currencyId], onDelete: NoAction, onUpdate: NoAction, map: "paymentRail_ibfk_2")
  wire             wire[]

  @@index([currencyId], map: "currencyId")
  @@index([paymentServiceId], map: "paymentServiceId")
}

model paymentService {
  paymentServiceId Int           @id @default(autoincrement())
  name             String        @unique(map: "name") @db.VarChar(255)
  createdAt        DateTime?     @default(now()) @db.Timestamp(0)
  updatedAt        DateTime?     @default(now()) @db.Timestamp(0)
  deletedAt        DateTime?     @db.Timestamp(0)
  paymentRail      paymentRail[]
}

model paymentType {
  paymentTypeId     Int                 @id @default(autoincrement())
  paymentTypeName   String              @unique(map: "idx_paymentType_paymentTypeName__Unique") @db.VarChar(50)
  createdAt         DateTime?           @default(now()) @db.DateTime(0)
  updatedAt         DateTime?           @default(now()) @db.DateTime(0)
  deletedAt         DateTime?           @db.DateTime(0)
  platform          platform[]
  tierSet           tierSet[]
  volumeCombination volumeCombination[]
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model platform {
  platformId                 Int                          @id @default(autoincrement())
  platformCode               String                       @unique(map: "idx_Platform_PlatformCode__Unique") @db.VarChar(10)
  platformName               String                       @db.VarChar(40)
  createdAt                  DateTime?                    @default(now()) @db.DateTime(0)
  updatedAt                  DateTime?                    @default(now()) @db.DateTime(0)
  deletedAt                  DateTime?                    @db.DateTime(0)
  displayOnUI                Int?                         @db.TinyInt
  settlementDescription      String?                      @db.VarChar(255)
  displaySequence            Int?                         @db.TinyInt
  paymentTypeId              Int?
  customerMonthlyTransaction customerMonthlyTransaction[]
  customerSettlements        customerSettlements[]
  merchantPlatform           merchantPlatform[]
  monthlyPlatformSettlement  monthlyPlatformSettlement[]
  nonMerchantFees            nonMerchantFees[]
  paymentType                paymentType?                 @relation(fields: [paymentTypeId], references: [paymentTypeId], onDelete: NoAction, map: "fk_platform_paymentType")
  volumeCombinationPlatform  volumeCombinationPlatform[]

  @@index([paymentTypeId], map: "fk_platform_paymentType_idx")
}

model province {
  provinceId           Int        @id @default(autoincrement())
  countryId            Int?
  taxRateId            Int?
  provinceName         String?    @db.VarChar(255)
  provinceAbbreviation String?    @db.VarChar(10)
  createdAt            DateTime?  @default(now()) @db.DateTime(0)
  updatedAt            DateTime?  @default(now()) @db.DateTime(0)
  deletedAt            DateTime?  @db.DateTime(0)
  customer             customer[]
  country              country?   @relation(fields: [countryId], references: [countryId], onDelete: NoAction, map: "fk_country")
  taxRates             taxRates?  @relation(fields: [taxRateId], references: [taxRateId], onDelete: NoAction, map: "fk_province_taxRates")

  @@index([countryId], map: "fk_province_country")
  @@index([taxRateId], map: "fk_province_taxRates_idx")
}

model role {
  roleId     Int        @id @default(autoincrement())
  name       String     @unique(map: "name") @db.VarChar(255)
  isEditable Boolean    @default(true)
  rule       Json?
  createdAt  DateTime?  @default(now()) @db.Timestamp(0)
  updatedAt  DateTime?  @default(now()) @db.Timestamp(0)
  deletedAt  DateTime?  @db.Timestamp(0)
  userRole   userRole[]
}

model settlementState {
  settlementStateId Int       @id @default(autoincrement())
  generationType    String    @db.VarChar(45)
  generationStatus  String    @db.VarChar(45)
  stateName         String    @db.VarChar(45)
  createdAt         DateTime? @default(now()) @db.Timestamp(0)
  updatedAt         DateTime? @default(now()) @db.Timestamp(0)
  deletedAt         DateTime? @db.Timestamp(0)
}

model staging_table {
  id            Int     @id @default(autoincrement())
  toMeta        String? @db.VarChar(1000)
  serviceNumber String? @db.VarChar(25)
}

model statementFrequency {
  statementFrequencyId   Int                     @id @default(autoincrement())
  statementFrequencyCode String                  @unique(map: "idx_StatementFrequency_StatementFrequencyCode__Unique") @db.VarChar(10)
  statementFrequencyName String                  @db.VarChar(40)
  createdAt              DateTime?               @default(now()) @db.DateTime(0)
  updatedAt              DateTime?               @default(now()) @db.DateTime(0)
  deletedAt              DateTime?               @db.DateTime(0)
  customerCustomerType   customerCustomerType[]
  customerSettlementJob  customerSettlementJob[]
  volumeCombination      volumeCombination[]
  wire                   wire[]
}

model taxRateItems {
  taxRateItemId     Int       @id @default(autoincrement())
  taxRateId         Int
  taxTypeId         Int
  taxRatePercentage Decimal?  @db.Decimal(8, 5)
  fromDate          DateTime? @db.Date
  toDate            DateTime? @db.Date
  createdAt         DateTime? @default(now()) @db.DateTime(0)
  updatedAt         DateTime? @default(now()) @db.DateTime(0)
  deletedAt         DateTime? @db.DateTime(0)
  taxTypes          taxTypes  @relation(fields: [taxTypeId], references: [taxTypeId], map: "fk_TaxRateItem_taxType")
  taxRates          taxRates  @relation(fields: [taxRateId], references: [taxRateId], map: "fk_taxRateItem_taxRate")

  @@index([taxRateId], map: "FK_taxRateItem_taxRate_idx")
  @@index([taxTypeId], map: "fk_TaxRateItem_taxType_idx")
}

model taxRates {
  taxRateId    Int            @id @default(autoincrement())
  taxRateName  String?        @db.VarChar(255)
  createdAt    DateTime?      @default(now()) @db.DateTime(0)
  updatedAt    DateTime?      @default(now()) @db.DateTime(0)
  deletedAt    DateTime?      @db.DateTime(0)
  province     province[]
  taxRateItems taxRateItems[]
}

model taxTypes {
  taxTypeId                Int                        @id @default(autoincrement())
  taxTypeAbbreviation      String?                    @db.VarChar(10)
  taxTypeName              String?                    @db.VarChar(255)
  createdAt                DateTime?                  @default(now()) @db.DateTime(0)
  updatedAt                DateTime?                  @default(now()) @db.DateTime(0)
  deletedAt                DateTime?                  @db.DateTime(0)
  customerSettlementKycTax customerSettlementKycTax[]
  taxRateItems             taxRateItems[]
}

model tierItem {
  tierItemId          Int                   @id @default(autoincrement())
  tierSetId           Int
  maxAmount           Decimal               @db.Decimal(14, 2)
  salesFee            Decimal               @db.Decimal(12, 8)
  minAmount           Decimal               @default(0.00) @db.Decimal(14, 2)
  createdAt           DateTime?             @default(now()) @db.DateTime(0)
  updatedAt           DateTime?             @default(now()) @db.DateTime(0)
  deletedAt           DateTime?             @db.DateTime(0)
  transactionFee      Decimal?              @default(0.00000000) @db.Decimal(12, 8)
  customerSettlements customerSettlements[]
  merchantPlatform    merchantPlatform[]
  tierSet             tierSet               @relation(fields: [tierSetId], references: [tierSetId], onDelete: NoAction, onUpdate: NoAction, map: "fk_tier_set")

  @@index([tierSetId], map: "fk_TierItem_TierFeeSet")
}

model tierSet {
  tierSetId                                                          Int                   @id @default(autoincrement())
  tierSetTypeId                                                      Int
  tierSetName                                                        String                @unique(map: "idx_TierSet_TierSetName__Unique") @db.VarChar(255)
  createdAt                                                          DateTime?             @default(now()) @db.DateTime(0)
  updatedAt                                                          DateTime?             @default(now()) @db.DateTime(0)
  deletedAt                                                          DateTime?             @db.DateTime(0)
  fromDate                                                           DateTime?             @db.Date
  toDate                                                             DateTime?             @db.Date
  feeIsPercentage                                                    Int?                  @db.TinyInt
  kycTypeId                                                          Int?
  paymentTypeId                                                      Int?
  merchantPlatform_merchantPlatform_agentTierSetIdTotierSet          merchantPlatform[]    @relation("merchantPlatform_agentTierSetIdTotierSet")
  merchantPlatform_merchantPlatform_subAgentTierSetIdTotierSet       merchantPlatform[]    @relation("merchantPlatform_subAgentTierSetIdTotierSet")
  merchantPlatform_merchantPlatform_subAgentSaleTierSetIdTotierSet   merchantPlatform[]    @relation("merchantPlatform_subAgentSaleTierSetIdTotierSet")
  merchantPlatform_merchantPlatform_agentSaleTierSetIdTotierSet      merchantPlatform[]    @relation("merchantPlatform_agentSaleTierSetIdTotierSet")
  merchantPlatform_merchantPlatform_clientTierSetIdTotierSet         merchantPlatform[]    @relation("merchantPlatform_clientTierSetIdTotierSet")
  merchantPlatform_merchantPlatform_rejectOneTierSetIdTotierSet      merchantPlatform[]    @relation("merchantPlatform_rejectOneTierSetIdTotierSet")
  merchantPlatform_merchantPlatform_integratorTierSetIdTotierSet     merchantPlatform[]    @relation("merchantPlatform_integratorTierSetIdTotierSet")
  merchantPlatform_merchantPlatform_integratorSaleTierSetIdTotierSet merchantPlatform[]    @relation("merchantPlatform_integratorSaleTierSetIdTotierSet")
  merchantPlatformKyc                                                merchantPlatformKyc[]
  tierItem                                                           tierItem[]
  tierSetType                                                        tierSetType           @relation(fields: [tierSetTypeId], references: [tierSetTypeId], onDelete: NoAction, onUpdate: NoAction, map: "fk_TierSet_TierSetType")
  paymentType                                                        paymentType?          @relation(fields: [paymentTypeId], references: [paymentTypeId], onDelete: NoAction, map: "fk_tierSet_paymentType")

  @@index([tierSetTypeId], map: "fk_TierSet_TierSetType")
  @@index([paymentTypeId], map: "fk_tierSet_paymentType_idx")
}

model tierSetType {
  tierSetTypeId   Int       @id @default(autoincrement())
  tierSetTypeName String    @unique(map: "idx_TierSetType_TierSetTypeName__Unique") @db.VarChar(50)
  createdAt       DateTime? @default(now()) @db.DateTime(0)
  updatedAt       DateTime? @default(now()) @db.DateTime(0)
  deletedAt       DateTime? @db.DateTime(0)
  tierSet         tierSet[]
}

model user {
  userId                                             Int                         @id @default(autoincrement())
  userNotificationId                                 Int
  userTypeId                                         Int
  userKey                                            String                      @db.VarChar(255)
  userName                                           String                      @db.VarChar(100)
  userEmail                                          String                      @db.VarChar(50)
  enteredByUser                                      String?                     @db.VarChar(100)
  userPermission                                     Int?
  createdAt                                          DateTime?                   @default(now()) @db.DateTime(0)
  updatedAt                                          DateTime?                   @default(now()) @db.DateTime(0)
  deletedAt                                          DateTime?                   @db.DateTime(0)
  deleteStatement                                    Boolean?
  lastPasswordUpdatedAt                              DateTime?                   @db.DateTime(0)
  lastSuccessfulLoginAt                              DateTime?                   @db.DateTime(0)
  lastFailedLoginAt                                  DateTime?                   @db.DateTime(0)
  unsuccessfulLoginCounter                           Int?                        @default(0)
  totpCode                                           String?                     @db.VarChar(256)
  lastTotpCodeAddedAt                                DateTime?                   @db.DateTime(0)
  audit                                              audit[]
  beneficiary                                        beneficiary[]
  customerSettlementJob                              customerSettlementJob[]
  email                                              email[]
  monthlyAllSettlementJob                            monthlyAllSettlementJob[]
  monthlyCustomerSettlement                          monthlyCustomerSettlement[]
  userPermission_user_userPermissionTouserPermission userPermission?             @relation("user_userPermissionTouserPermission", fields: [userPermission], references: [userPermissionId], onDelete: NoAction, onUpdate: NoAction, map: "user_ibfk_1")
  userPreference                                     userPreference?
  userRole                                           userRole[]
  wire_wire_journalCreatedByIdTouser                 wire[]                      @relation("wire_journalCreatedByIdTouser")
  wire_wire_wirePendingUpdatedByIdTouser             wire[]                      @relation("wire_wirePendingUpdatedByIdTouser")
  wire_wire_wireApprovedByIdTouser                   wire[]                      @relation("wire_wireApprovedByIdTouser")
  wire_wire_wireGeneratedByIdTouser                  wire[]                      @relation("wire_wireGeneratedByIdTouser")

  @@index([userPermission], map: "userPermission")
}

model userNotification {
  userNotificationId   Int    @id @default(autoincrement())
  userNotificationName String @db.VarChar(40)
}

model userPermission {
  userPermissionId                         Int       @id
  userPermissionValue                      Int?
  userPermissionDescription                String?   @db.VarChar(255)
  createdAt                                DateTime? @default(now()) @db.DateTime(0)
  updatedAt                                DateTime? @default(now()) @db.DateTime(0)
  deletedAt                                DateTime? @db.DateTime(0)
  user_user_userPermissionTouserPermission user[]    @relation("user_userPermissionTouserPermission")
}

model userPreference {
  userId               Int       @id
  createdAt            DateTime? @default(now()) @db.Timestamp(0)
  updatedAt            DateTime? @default(now()) @db.Timestamp(0)
  deletedAt            DateTime? @db.Timestamp(0)
  emailPreference      Json?
  fileSystemPreference Json?
  user                 user      @relation(fields: [userId], references: [userId], onDelete: NoAction, onUpdate: NoAction, map: "userpreference_ibfk_1")
}

model userRole {
  userRoleId Int       @id @default(autoincrement())
  userId     Int
  roleId     Int
  createdAt  DateTime? @default(now()) @db.Timestamp(0)
  updatedAt  DateTime? @default(now()) @db.Timestamp(0)
  deletedAt  DateTime? @db.Timestamp(0)
  user       user      @relation(fields: [userId], references: [userId], onDelete: NoAction, onUpdate: NoAction, map: "userRole_ibfk_1")
  role       role      @relation(fields: [roleId], references: [roleId], onDelete: NoAction, onUpdate: NoAction, map: "userRole_ibfk_2")

  @@unique([userId, roleId], map: "unique_user_role")
  @@index([roleId], map: "roleId")
}

model userType {
  userTypeId   Int       @id @default(autoincrement())
  userTypeName String    @db.VarChar(40)
  createdAt    DateTime? @default(now()) @db.DateTime(0)
  updatedAt    DateTime? @default(now()) @db.DateTime(0)
  deletedAt    DateTime? @db.DateTime(0)
}

model volumeCombination {
  id                        Int                         @id @default(autoincrement())
  fromDate                  DateTime                    @db.Date
  paymentTypeId             Int
  statementFrequencyId      Int
  customerCustomerTypeId    Int
  deletedAt                 DateTime?                   @db.Date
  customerCustomerType      customerCustomerType        @relation(fields: [customerCustomerTypeId], references: [customerCustomerTypeId], onDelete: NoAction, onUpdate: NoAction, map: "volumeCombination_customerCustomerType_FK")
  paymentType               paymentType                 @relation(fields: [paymentTypeId], references: [paymentTypeId], onDelete: NoAction, onUpdate: NoAction, map: "volumeCombination_paymentType_FK")
  statementFrequency        statementFrequency          @relation(fields: [statementFrequencyId], references: [statementFrequencyId], onDelete: NoAction, onUpdate: NoAction, map: "volumeCombination_statementFrequency_FK")
  volumeCombinationPlatform volumeCombinationPlatform[]

  @@index([customerCustomerTypeId], map: "volumeCombination_customerCustomerType_FK")
  @@index([paymentTypeId], map: "volumeCombination_paymentType_FK")
  @@index([statementFrequencyId], map: "volumeCombination_statementFrequency_FK")
}

model volumeCombinationPlatform {
  id                  Int               @id @default(autoincrement())
  volumeCombinationId Int
  platformId          Int
  enabled             Boolean           @default(false)
  deletedAt           DateTime?         @db.Date
  platform            platform          @relation(fields: [platformId], references: [platformId], onDelete: NoAction, onUpdate: NoAction, map: "volumeCombinationPlatform_platform_FK")
  volumeCombination   volumeCombination @relation(fields: [volumeCombinationId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "volumeCombinationPlatform_volumeCombination_FK")

  @@index([platformId], map: "volumeCombinationPlatform_platform_FK")
  @@index([volumeCombinationId], map: "volumeCombinationPlatform_volumeCombination_FK")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model wire {
  wireId                                 Int                  @id @default(autoincrement())
  expectedWireDate                       DateTime             @db.Date
  finalWireDate                          DateTime             @db.Date
  expectedWireAmount                     Float
  finalWireAmount                        Float
  isHold                                 Boolean?             @default(true)
  reference                              String?              @db.VarChar(255)
  frequencyId                            Int
  isCancelled                            Boolean?             @default(false)
  cancelledById                          Int?
  cancelledAt                            DateTime?            @db.Timestamp(0)
  customerId                             Int
  generatedBeneficiaryId                 Int?
  settlementId                           Int?
  paymentRailId                          Int?
  customerWireInOutId                    Int?
  wirePendingUpdatedById                 Int?
  wirePendingUpdatedAt                   DateTime?            @db.Timestamp(0)
  wireApprovedById                       Int?
  wireApprovedAt                         DateTime?            @db.Timestamp(0)
  wireGeneratedById                      Int?
  wireGeneratedAt                        DateTime?            @db.Timestamp(0)
  journalCreatedById                     Int?
  journalCreatedAt                       DateTime?            @db.Timestamp(0)
  createdAt                              DateTime?            @default(now()) @db.Timestamp(0)
  updatedAt                              DateTime?            @default(now()) @db.Timestamp(0)
  deletedAt                              DateTime?            @db.Timestamp(0)
  email                                  email[]
  customer                               customer             @relation(fields: [customerId], references: [customerId], onDelete: NoAction, onUpdate: NoAction, map: "wire_ibfk_1")
  user_wire_journalCreatedByIdTouser     user?                @relation("wire_journalCreatedByIdTouser", fields: [journalCreatedById], references: [userId], onDelete: NoAction, onUpdate: NoAction, map: "wire_ibfk_10")
  statementFrequency                     statementFrequency   @relation(fields: [frequencyId], references: [statementFrequencyId], onDelete: NoAction, onUpdate: NoAction, map: "wire_ibfk_2")
  beneficiary                            beneficiary?         @relation(fields: [generatedBeneficiaryId], references: [beneficiaryId], onDelete: NoAction, onUpdate: NoAction, map: "wire_ibfk_3")
  customerSettlements                    customerSettlements? @relation(fields: [settlementId], references: [customerSettlementsId], onDelete: NoAction, onUpdate: NoAction, map: "wire_ibfk_4")
  customerWireInOuts                     customerWireInOuts?  @relation(fields: [customerWireInOutId], references: [customerWireInPayOutId], onDelete: NoAction, onUpdate: NoAction, map: "wire_ibfk_5")
  paymentRail                            paymentRail?         @relation(fields: [paymentRailId], references: [paymentRailId], onDelete: NoAction, onUpdate: NoAction, map: "wire_ibfk_6")
  user_wire_wirePendingUpdatedByIdTouser user?                @relation("wire_wirePendingUpdatedByIdTouser", fields: [wirePendingUpdatedById], references: [userId], onDelete: NoAction, onUpdate: NoAction, map: "wire_ibfk_7")
  user_wire_wireApprovedByIdTouser       user?                @relation("wire_wireApprovedByIdTouser", fields: [wireApprovedById], references: [userId], onDelete: NoAction, onUpdate: NoAction, map: "wire_ibfk_8")
  user_wire_wireGeneratedByIdTouser      user?                @relation("wire_wireGeneratedByIdTouser", fields: [wireGeneratedById], references: [userId], onDelete: NoAction, onUpdate: NoAction, map: "wire_ibfk_9")

  @@index([customerId], map: "customerId")
  @@index([customerWireInOutId], map: "customerWireInOutId")
  @@index([frequencyId], map: "frequencyId")
  @@index([generatedBeneficiaryId], map: "generatedBeneficiaryId")
  @@index([isCancelled], map: "idx_isCancelled")
  @@index([journalCreatedById], map: "journalCreatedById")
  @@index([paymentRailId], map: "paymentRailId")
  @@index([settlementId], map: "settlementId")
  @@index([wireApprovedById], map: "wireApprovedById")
  @@index([wireGeneratedById], map: "wireGeneratedById")
  @@index([wirePendingUpdatedById], map: "wirePendingUpdatedById")
}

model wireConfiguration {
  wireConfigurationId Int         @id @default(autoincrement())
  isHold              Boolean?    @default(false)
  thresholdAmount     Float?      @default(0)
  comment             String?     @db.VarChar(255)
  createdAt           DateTime?   @default(now()) @db.Timestamp(0)
  updatedAt           DateTime?   @default(now()) @db.Timestamp(0)
  deletedAt           DateTime?   @db.Timestamp(0)
  wireGroup           wireGroup[]
}

model wireGroup {
  wireGroupId         Int               @id @default(autoincrement())
  beneficiaryId       Int?              @unique(map: "beneficiaryId")
  wireConfigurationId Int
  createdAt           DateTime?         @default(now()) @db.Timestamp(0)
  updatedAt           DateTime?         @default(now()) @db.Timestamp(0)
  deletedAt           DateTime?         @db.Timestamp(0)
  customer            customer[]
  beneficiary         beneficiary?      @relation(fields: [beneficiaryId], references: [beneficiaryId], onDelete: NoAction, onUpdate: NoAction, map: "wireGroup_ibfk_1")
  wireConfiguration   wireConfiguration @relation(fields: [wireConfigurationId], references: [wireConfigurationId], onDelete: NoAction, onUpdate: NoAction, map: "wireGroup_ibfk_2")

  @@index([wireConfigurationId], map: "wireConfigurationId")
}

model settlementState {
  settlementStateId Int       @id @default(autoincrement())
  generationType    String    @db.VarChar(45)
  generationStatus  String    @db.VarChar(45)
  stateName         String    @db.VarChar(45)
  createdAt         DateTime? @default(now()) @db.Timestamp(0)
  updatedAt         DateTime? @default(now()) @db.Timestamp(0)
  deletedAt         DateTime? @db.Timestamp(0)
}

model userPreference {
  userId               Int       @id
  createdAt            DateTime? @default(now()) @db.Timestamp(0)
  updatedAt            DateTime? @default(now()) @db.Timestamp(0)
  deletedAt            DateTime? @db.Timestamp(0)
  emailPreference      Json?
  fileSystemPreference Json?
  user                 user      @relation(fields: [userId], references: [userId], onDelete: NoAction, onUpdate: NoAction, map: "userPreference_ibfk_1")
}

enum monthlyAllSettlementJob_status {
  PROGRESS
  SUCCESS
  ERROR
}

enum monthlyTransactionCalculationJob_status {
  PROGRESS
  SUCCESS
  ERROR
}

enum customerSettlementJob_status {
  PROGRESS
  SUCCESS
  ERROR
}

enum monthlyCustomerSettlement_status {
  SUCCESS
  ERROR
  SKIPPED
}

enum customerMonthlyTransaction_status {
  FAILED
  CREATED
  APPROVED
}