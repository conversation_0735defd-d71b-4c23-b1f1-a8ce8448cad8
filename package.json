{"type": "module", "engines": {"node": "20.18.0"}, "scripts": {"dev": "tsx watch -r dotenv/config src", "lint": "tsc --noEmit && eslint --fix --cache . && prettier --write .", "lint:check": "tsc --noEmit && eslint --cache .", "build": "rimraf dist && esbuild `find src \\( -name '*.ts' \\)` --platform=node --target=node20.18.0 --outdir=dist && tsc-alias", "start:dev": "COMPILED=true node -r dotenv/config dist", "start:prod": "COMPILED=true node dist", "test:unit": "vitest run", "test:dev": "vitest dev", "test:ui": "vitest dev --ui", "test:coverage": "vitest run --coverage", "prepare": "node -e \"if (!['production','ci'].includes(process.env.NODE_ENV)){process.exit(1)} \" || husky"}, "lint-staged": {"**/*.ts": "eslint --fix", "**/*.{js,cjs,json,md,yml,html,css}": "prettier --write"}, "dependencies": {"@aws-sdk/client-lambda": "3.699.0", "@fastify/autoload": "6.0.2", "@fastify/cookie": "11.0.1", "@fastify/cors": "10.0.1", "@fastify/helmet": "12.0.1", "@fastify/jwt": "9.0.1", "@fastify/static": "8.0.2", "@fastify/swagger": "9.2.0", "@fastify/swagger-ui": "5.1.0", "@prisma/client": "5.21.1", "bcrypt": "5.1.1", "date-fns": "4.1.0", "env-var": "7.5.0", "fastify": "5.0.0", "fastify-plugin": "5.0.1", "handlebars": "4.7.8", "jsonwebtoken": "9.0.2", "stream-json": "1.9.1", "uuid": "10.0.0"}, "devDependencies": {"@commitlint/cli": "19.5.0", "@commitlint/config-conventional": "19.5.0", "@types/bcrypt": "5.0.2", "@types/jsonwebtoken": "9.0.6", "@types/node": "20.11.28", "@types/stream-json": "1.7.8", "@typescript-eslint/eslint-plugin": "7.3.0", "@typescript-eslint/parser": "7.3.0", "@vitest/coverage-v8": "2.1.2", "@vitest/ui": "2.1.2", "dotenv": "16.4.5", "esbuild": "0.24.0", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-config-xo": "0.44.0", "eslint-config-xo-typescript": "4.0.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-unicorn": "51.0.1", "eslint-plugin-vitest": "0.3.26", "eslint-plugin-vitest-globals": "1.5.0", "husky": "9.1.6", "lint-staged": "15.2.10", "pino-pretty": "11.2.2", "prettier": "3.3.3", "prisma": "5.21.1", "rimraf": "6.0.1", "tsc-alias": "1.8.10", "tsx": "4.19.1", "typescript": "5.4.2", "vite-tsconfig-paths": "5.0.1", "vitest": "2.1.2"}}